<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอินและสิทธิ์
requireLogin();
requireModulePermission('schedules', 'view');

$database = new Database();
$db = $database->getConnection();

$message = '';
$action = $_GET['action'] ?? 'calendar';
$schedule_id = $_GET['id'] ?? null;
$view_date = $_GET['date'] ?? date('Y-m-d');

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_schedule']) && $auth->hasModulePermission('schedules', 'add')) {
        $date_mode = $_POST['date_mode'] ?? 'single';
        $dates_to_create = [];

        // สร้างรายการวันที่ตามโหมดที่เลือก
        if ($date_mode === 'single') {
            $dates_to_create[] = $_POST['schedule_date'];
        } elseif ($date_mode === 'multiple') {
            $start_date = new DateTime($_POST['start_date']);
            $end_date = new DateTime($_POST['end_date']);

            while ($start_date <= $end_date) {
                $dates_to_create[] = $start_date->format('Y-m-d');
                $start_date->add(new DateInterval('P1D'));
            }
        } elseif ($date_mode === 'weekly') {
            $start_date = new DateTime($_POST['pattern_start_date']);
            $weeks_count = intval($_POST['weeks_count']);
            $selected_days = $_POST['selected_days'] ?? [];

            $day_mapping = [
                'sunday' => 0, 'monday' => 1, 'tuesday' => 2, 'wednesday' => 3,
                'thursday' => 4, 'friday' => 5, 'saturday' => 6
            ];

            for ($week = 0; $week < $weeks_count; $week++) {
                foreach ($selected_days as $day) {
                    if (isset($day_mapping[$day])) {
                        $target_day = $day_mapping[$day];
                        $date = clone $start_date;
                        $date->add(new DateInterval('P' . ($week * 7) . 'D'));

                        // คำนวณวันที่ถูกต้องในสัปดาห์
                        $days_to_add = ($target_day - $date->format('w') + 7) % 7;
                        $date->add(new DateInterval('P' . $days_to_add . 'D'));

                        $dates_to_create[] = $date->format('Y-m-d');
                    }
                }
            }

            // เรียงลำดับวันที่
            sort($dates_to_create);
            // ลบวันที่ซ้ำ
            $dates_to_create = array_unique($dates_to_create);
        } elseif ($date_mode === 'monthly') {
            $target_month = $_POST['target_month']; // Format: YYYY-MM
            $pattern_type = $_POST['monthly_pattern_type'] ?? 'all_days';
            $exclude_holidays = isset($_POST['exclude_holidays']);
            $monthly_selected_days = $_POST['monthly_selected_days'] ?? [];

            // สร้างวันที่เริ่มต้นและสิ้นสุดของเดือน
            $month_start = new DateTime($target_month . '-01');
            $month_end = new DateTime($month_start->format('Y-m-t'));

            $day_mapping = [
                'sunday' => 0, 'monday' => 1, 'tuesday' => 2, 'wednesday' => 3,
                'thursday' => 4, 'friday' => 5, 'saturday' => 6
            ];

            // วนลูปทุกวันในเดือน
            $current_date = clone $month_start;
            while ($current_date <= $month_end) {
                $day_of_week = intval($current_date->format('w'));
                $should_include = false;

                // ตรวจสอบตามรูปแบบที่เลือก
                switch ($pattern_type) {
                    case 'all_days':
                        $should_include = true;
                        break;
                    case 'weekdays_only':
                        $should_include = ($day_of_week >= 1 && $day_of_week <= 5); // จันทร์-ศุกร์
                        break;
                    case 'weekends_only':
                        $should_include = ($day_of_week == 0 || $day_of_week == 6); // เสาร์-อาทิตย์
                        break;
                    case 'custom_days':
                        foreach ($monthly_selected_days as $selected_day) {
                            if (isset($day_mapping[$selected_day]) && $day_mapping[$selected_day] == $day_of_week) {
                                $should_include = true;
                                break;
                            }
                        }
                        break;
                }

                // ตรวจสอบการข้ามวันหยุด
                if ($should_include && $exclude_holidays) {
                    // ข้ามวันเสาร์-อาทิตย์ (ถ้าไม่ได้เลือกเป็น weekends_only)
                    if ($pattern_type !== 'weekends_only' && ($day_of_week == 0 || $day_of_week == 6)) {
                        $should_include = false;
                    }

                    // ข้ามวันหยุดนักขัตฤกษ์ (สามารถเพิ่มเติมได้)
                    $holidays = getThaiHolidays($current_date->format('Y'));
                    if (in_array($current_date->format('Y-m-d'), $holidays)) {
                        $should_include = false;
                    }
                }

                if ($should_include) {
                    $dates_to_create[] = $current_date->format('Y-m-d');
                }

                $current_date->add(new DateInterval('P1D'));
            }
        }

        // สร้างตารางเวรสำหรับแต่ละวันที่
        $success_count = 0;
        $error_count = 0;
        $duplicate_count = 0;

        $query = "INSERT INTO schedules (doctor_id, shift_type_id, schedule_date, notes, created_by) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);

        // ตรวจสอบตารางเวรที่มีอยู่แล้ว
        $check_query = "SELECT COUNT(*) FROM schedules WHERE doctor_id = ? AND shift_type_id = ? AND schedule_date = ?";
        $check_stmt = $db->prepare($check_query);

        foreach ($dates_to_create as $date) {
            // ตรวจสอบว่ามีตารางเวรซ้ำหรือไม่
            $check_stmt->execute([$_POST['doctor_id'], $_POST['shift_type_id'], $date]);
            $exists = $check_stmt->fetchColumn();

            if ($exists > 0) {
                $duplicate_count++;
                continue;
            }

            // สร้างตารางเวรใหม่
            try {
                if ($stmt->execute([
                    $_POST['doctor_id'],
                    $_POST['shift_type_id'],
                    $date,
                    $_POST['notes'],
                    $_SESSION['username'] ?? 'admin'
                ])) {
                    $success_count++;
                    $auth->logActivity('add_schedule', 'schedules', $db->lastInsertId(), null, array_merge($_POST, ['schedule_date' => $date]));
                }
            } catch (PDOException $e) {
                $error_count++;
            }
        }

        // แสดงผลลัพธ์
        $messages = [];
        if ($success_count > 0) {
            $messages[] = "สร้างตารางเวรสำเร็จ {$success_count} รายการ";
        }
        if ($duplicate_count > 0) {
            $messages[] = "ข้ามตารางเวรที่มีอยู่แล้ว {$duplicate_count} รายการ";
        }
        if ($error_count > 0) {
            $messages[] = "เกิดข้อผิดพลาด {$error_count} รายการ";
        }

        if ($success_count > 0) {
            $message = showAlert(implode(', ', $messages), 'success');
        } elseif ($duplicate_count > 0 && $error_count === 0) {
            $message = showAlert(implode(', ', $messages), 'warning');
        } else {
            $message = showAlert('เกิดข้อผิดพลาดในการเพิ่มตารางเวร', 'error');
        }

        $action = 'calendar';
    }
    
    if (isset($_POST['update_schedule']) && $auth->hasModulePermission('schedules', 'edit')) {
        $query = "UPDATE schedules SET doctor_id=?, shift_type_id=?, schedule_date=?, notes=?, status=? WHERE id=?";
        $stmt = $db->prepare($query);
        
        // ดึงข้อมูลเก่าสำหรับ log
        $old_data_query = "SELECT * FROM schedules WHERE id = ?";
        $old_stmt = $db->prepare($old_data_query);
        $old_stmt->execute([$schedule_id]);
        $old_data = $old_stmt->fetch();

        if ($stmt->execute([
            $_POST['doctor_id'],
            $_POST['shift_type_id'],
            $_POST['schedule_date'],
            $_POST['notes'],
            $_POST['status'],
            $schedule_id
        ])) {
            $auth->logActivity('update_schedule', 'schedules', $schedule_id, $old_data, $_POST);
            $message = showAlert('อัปเดตตารางเวรเรียบร้อยแล้ว', 'success');
            $action = 'calendar';
        } else {
            $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตตารางเวร', 'error');
        }
    }
    
    if (isset($_POST['delete_schedule']) && $auth->hasModulePermission('schedules', 'delete')) {
        $delete_schedule_id = $_POST['schedule_id'] ?? null;

        if ($delete_schedule_id) {
            // ดึงข้อมูลเก่าสำหรับ log
            $old_data_query = "SELECT * FROM schedules WHERE id = ?";
            $old_stmt = $db->prepare($old_data_query);
            $old_stmt->execute([$delete_schedule_id]);
            $old_data = $old_stmt->fetch();

            $query = "DELETE FROM schedules WHERE id = ?";
            $stmt = $db->prepare($query);

            if ($stmt->execute([$delete_schedule_id])) {
                $auth->logActivity('delete_schedule', 'schedules', $delete_schedule_id, $old_data);
                $message = showAlert('ลบตารางเวรเรียบร้อยแล้ว', 'success');
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการลบตารางเวร', 'error');
            }
        } else {
            $message = showAlert('ไม่พบข้อมูลตารางเวรที่ต้องการลบ', 'error');
        }
        $action = 'calendar';
    }
}

// Get doctors and shift types for dropdowns
$doctors_query = "SELECT d.*, dept.name as department_name FROM doctors d LEFT JOIN departments dept ON d.department_id = dept.id WHERE d.status = 'active' ORDER BY d.doctor_code";
$doctors_stmt = $db->prepare($doctors_query);
$doctors_stmt->execute();
$doctors = $doctors_stmt->fetchAll();

$shifts_query = "SELECT * FROM shift_types ORDER BY start_time";
$shifts_stmt = $db->prepare($shifts_query);
$shifts_stmt->execute();
$shift_types = $shifts_stmt->fetchAll();

// Get schedule data for edit
$schedule = null;
if ($action == 'edit' && $schedule_id) {
    $query = "SELECT * FROM schedules WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$schedule_id]);
    $schedule = $stmt->fetch();
}

// Get calendar data
if ($action == 'calendar') {
    // ตรวจสอบโหมดการแสดง (default เป็นวันปัจจุบัน)
    $calendar_view_mode = $_GET['view_mode'] ?? 'today';

    // กำหนดช่วงวันที่ตามโหมด
    switch ($calendar_view_mode) {
        case 'month':
            $start_date = date('Y-m-01', strtotime($view_date)); // First day of month
            $end_date = date('Y-m-t', strtotime($view_date)); // Last day of month
            break;
        case 'week':
            $start_date = date('Y-m-d', strtotime('monday this week', strtotime($view_date)));
            $end_date = date('Y-m-d', strtotime('sunday this week', strtotime($view_date)));
            break;
        case 'today':
        default:
            $start_date = date('Y-m-d'); // วันปัจจุบัน
            $end_date = date('Y-m-d'); // วันปัจจุบัน
            break;
    }

    // Get filters for calendar
    $calendar_filter_doctor = $_GET['calendar_doctor'] ?? null;
    $calendar_filter_department = $_GET['calendar_department'] ?? null;
    $calendar_search = $_GET['calendar_search'] ?? null;

    $query = "SELECT s.*, d.fullname, d.doctor_code,
                     st.name as shift_name, st.start_time, st.end_time, st.color,
                     dept.name as department_name
              FROM schedules s
              JOIN doctors d ON s.doctor_id = d.id
              JOIN shift_types st ON s.shift_type_id = st.id
              LEFT JOIN departments dept ON d.department_id = dept.id
              WHERE s.schedule_date BETWEEN ? AND ?";

    $calendar_params = [$start_date, $end_date];

    if ($calendar_search) {
        $query .= " AND (d.doctor_code LIKE ? OR d.first_name LIKE ? OR d.last_name LIKE ? OR dept.name LIKE ? OR st.name LIKE ?)";
        $search_param = "%$calendar_search%";
        $calendar_params = array_merge($calendar_params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }

    if ($calendar_filter_doctor) {
        $query .= " AND s.doctor_id = ?";
        $calendar_params[] = $calendar_filter_doctor;
    }

    if ($calendar_filter_department) {
        $query .= " AND d.department_id = ?";
        $calendar_params[] = $calendar_filter_department;
    }

    $query .= " ORDER BY s.schedule_date, st.start_time";

    $stmt = $db->prepare($query);
    $stmt->execute($calendar_params);
    $schedules = $stmt->fetchAll();

    // Group schedules by date
    $calendar_data = [];
    foreach ($schedules as $sched) {
        $date = $sched['schedule_date'];
        if (!isset($calendar_data[$date])) {
            $calendar_data[$date] = [];
        }
        $calendar_data[$date][] = $sched;
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดตารางเวร - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-calendar-check me-2"></i>
                            จัดตารางเวร
                        </h1>
                        <p class="text-muted">จัดการตารางเวรแพทย์</p>
                    </div>
                    <div class="btn-group">
                        <a href="?action=calendar" class="btn btn-outline-primary <?php echo $action == 'calendar' ? 'active' : ''; ?>">
                            <i class="fas fa-calendar me-1"></i>ปฏิทิน
                        </a>
                        <a href="?action=list" class="btn btn-outline-primary <?php echo $action == 'list' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-1"></i>รายการ
                        </a>
                        <?php if ($auth->hasModulePermission('schedules', 'add')): ?>
                            <a href="?action=add" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>เพิ่มเวร
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($action == 'calendar'): ?>
            <!-- Calendar View -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <h6 class="card-title mb-1">
                                <i class="fas fa-calendar me-1"></i>
                                ปฏิทินตารางเวร
                                <?php if ($calendar_view_mode == 'today'): ?>
                                    - วันนี้
                                <?php elseif ($calendar_view_mode == 'week'): ?>
                                    - สัปดาห์ที่ <?php echo date('W', strtotime($view_date)); ?>
                                <?php else: ?>
                                    - <?php echo getThaiMonthName($view_date) . ' ' . (date('Y', strtotime($view_date)) + 543); ?>
                                <?php endif; ?>
                            </h6>

                            <?php if ($calendar_search): ?>
                                <small class="text-primary">
                                    <i class="fas fa-search me-1"></i>
                                    ค้นหา: "<?php echo htmlspecialchars($calendar_search); ?>"
                                </small>
                            <?php endif; ?>

                            <?php if (!empty($calendar_data)): ?>
                                <small class="text-muted">
                                    <?php echo count($calendar_data); ?> วัน, <?php echo array_sum(array_map('count', $calendar_data)); ?> ตารางเวร
                                </small>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex gap-2 align-items-center">
                            <!-- View Mode Selector -->
                            <div class="btn-group" role="group">
                                <a href="?action=calendar&view_mode=today" class="btn btn-sm <?php echo $calendar_view_mode == 'today' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    <i class="fas fa-calendar-day me-1"></i>วันนี้
                                </a>
                                <a href="?action=calendar&view_mode=week&date=<?php echo date('Y-m-d'); ?>" class="btn btn-sm <?php echo $calendar_view_mode == 'week' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    <i class="fas fa-calendar-week me-1"></i>สัปดาห์
                                </a>
                                <a href="?action=calendar&view_mode=month&date=<?php echo date('Y-m-d'); ?>" class="btn btn-sm <?php echo $calendar_view_mode == 'month' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    <i class="fas fa-calendar me-1"></i>เดือน
                                </a>
                            </div>

                            <?php if ($calendar_view_mode != 'today'): ?>
                                <!-- Date Navigation -->
                                <?php if ($calendar_view_mode == 'month'): ?>
                                    <input type="month" id="calendarMonth" class="form-control form-control-sm"
                                           value="<?php echo date('Y-m', strtotime($view_date)); ?>"
                                           onchange="changeCalendarMonth(this.value)" style="width: 150px;">
                                <?php endif; ?>

                                <div class="btn-group">
                                    <?php if ($calendar_view_mode == 'week'): ?>
                                        <a href="?action=calendar&view_mode=week&date=<?php echo date('Y-m-d', strtotime($view_date . ' -1 week')); ?>" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                        <a href="?action=calendar&view_mode=week&date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary btn-sm">สัปดาห์นี้</a>
                                        <a href="?action=calendar&view_mode=week&date=<?php echo date('Y-m-d', strtotime($view_date . ' +1 week')); ?>" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php else: ?>
                                        <a href="?action=calendar&view_mode=month&date=<?php echo date('Y-m-d', strtotime($view_date . ' -1 month')); ?>" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                        <a href="?action=calendar&view_mode=month&date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary btn-sm">เดือนนี้</a>
                                        <a href="?action=calendar&view_mode=month&date=<?php echo date('Y-m-d', strtotime($view_date . ' +1 month')); ?>" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Calendar Search and Filters -->
                    <div class="d-flex gap-2 align-items-center flex-wrap">
                        <!-- Search Box -->
                        <div class="input-group input-group-sm" style="width: 250px;">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" id="calendarSearch" class="form-control"
                                   placeholder="ค้นหาแพทย์, รหัส, แผนก..."
                                   value="<?php echo htmlspecialchars($_GET['calendar_search'] ?? ''); ?>"
                                   onkeyup="handleCalendarSearch(event)">
                            <?php if (!empty($_GET['calendar_search'])): ?>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearCalendarSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            <?php endif; ?>
                        </div>

                        <div class="vr"></div>
                        <small class="text-muted">กรอง:</small>

                        <select id="calendarFilterDoctor" class="form-select form-select-sm" onchange="applyCalendarFilters()">
                            <option value="">ทุกแพทย์</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?php echo $doctor['id']; ?>"
                                        <?php echo ($_GET['calendar_doctor'] ?? '') == $doctor['id'] ? 'selected' : ''; ?>>
                                    <?php echo $doctor['doctor_code'] . ' - ' . $doctor['first_name'] . ' ' . $doctor['last_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <select id="calendarFilterDepartment" class="form-select form-select-sm" onchange="applyCalendarFilters()">
                            <option value="">ทุกแผนก</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>"
                                        <?php echo ($_GET['calendar_department'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                    <?php echo $dept['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearCalendarFilters()" title="ล้างตัวกรอง">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body schedule-calendar">
                    <?php if (empty($calendar_data)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                            <?php if ($calendar_view_mode == 'today'): ?>
                                <h6 class="text-muted">ไม่พบตารางเวรวันนี้</h6>
                                <p class="text-muted small">
                                    <?php if ($calendar_search): ?>
                                        ไม่พบตารางเวรที่ตรงกับคำค้นหา "<?php echo htmlspecialchars($calendar_search); ?>" ในวันนี้
                                    <?php elseif ($calendar_filter_doctor || $calendar_filter_department): ?>
                                        ไม่พบตารางเวรตามเงื่อนไขที่เลือกในวันนี้
                                    <?php else: ?>
                                        ยังไม่มีการจัดตารางเวรสำหรับวันนี้
                                    <?php endif; ?>
                                </p>
                            <?php elseif ($calendar_view_mode == 'week'): ?>
                                <h6 class="text-muted">ไม่พบตารางเวรในสัปดาห์นี้</h6>
                                <p class="text-muted small">
                                    <?php if ($calendar_search): ?>
                                        ไม่พบตารางเวรที่ตรงกับคำค้นหา "<?php echo htmlspecialchars($calendar_search); ?>" ในสัปดาห์นี้
                                    <?php elseif ($calendar_filter_doctor || $calendar_filter_department): ?>
                                        ไม่พบตารางเวรตามเงื่อนไขที่เลือกในสัปดาห์นี้
                                    <?php else: ?>
                                        ยังไม่มีการจัดตารางเวรในสัปดาห์ที่ <?php echo date('W', strtotime($view_date)); ?>
                                    <?php endif; ?>
                                </p>
                            <?php else: ?>
                                <h6 class="text-muted">ไม่พบตารางเวรในเดือนนี้</h6>
                                <p class="text-muted small">
                                    <?php if ($calendar_search): ?>
                                        ไม่พบตารางเวรที่ตรงกับคำค้นหา "<?php echo htmlspecialchars($calendar_search); ?>" ในเดือนนี้
                                    <?php elseif ($calendar_filter_doctor || $calendar_filter_department): ?>
                                        ไม่พบตารางเวรตามเงื่อนไขที่เลือก
                                    <?php else: ?>
                                        ยังไม่มีการจัดตารางเวรในเดือน <?php echo getThaiMonthName($view_date) . ' ' . (date('Y', strtotime($view_date)) + 543); ?>
                                    <?php endif; ?>
                                </p>
                            <?php endif; ?>

                            <div class="mt-3">
                                <?php if ($auth->hasModulePermission('schedules', 'add')): ?>
                                    <a href="?action=add<?php echo $calendar_view_mode == 'today' ? '&date=' . date('Y-m-d') : ''; ?>" class="btn btn-primary btn-sm me-2">
                                        <i class="fas fa-plus me-1"></i>เพิ่มตารางเวรใหม่
                                    </a>
                                <?php endif; ?>

                                <?php if ($calendar_view_mode == 'today'): ?>
                                    <a href="?action=calendar&view_mode=week" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-calendar-week me-1"></i>ดูสัปดาห์นี้
                                    </a>
                                <?php elseif ($calendar_view_mode == 'week'): ?>
                                    <a href="?action=calendar&view_mode=month" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-calendar me-1"></i>ดูเดือนนี้
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- แสดงตารางเวรแบบรายการตามวันที่ -->
                        <div class="schedule-calendar">
                            <?php
                            $thai_days = [
                                'Sunday' => 'อาทิตย์',
                                'Monday' => 'จันทร์',
                                'Tuesday' => 'อังคาร',
                                'Wednesday' => 'พุธ',
                                'Thursday' => 'พฤหัสบดี',
                                'Friday' => 'ศุกร์',
                                'Saturday' => 'เสาร์'
                            ];

                            // เรียงลำดับวันที่
                            ksort($calendar_data);

                            foreach ($calendar_data as $date => $day_schedules):
                                $day_name = $thai_days[date('l', strtotime($date))];
                                $is_today = $date == date('Y-m-d');
                                $is_holiday = isHoliday($date);

                                // จัดกลุ่มตารางเวรตามประเภทเวร
                                $shifts_by_type = [];
                                foreach ($day_schedules as $sched) {
                                    $shift_key = $sched['shift_type_id'] . '_' . $sched['start_time'] . '_' . $sched['end_time'];
                                    if (!isset($shifts_by_type[$shift_key])) {
                                        $shifts_by_type[$shift_key] = [
                                            'shift_info' => $sched,
                                            'doctors' => []
                                        ];
                                    }
                                    $shifts_by_type[$shift_key]['doctors'][] = $sched;
                                }

                                // เรียงลำดับตามเวลาเริ่มต้น
                                uasort($shifts_by_type, function($a, $b) {
                                    return strcmp($a['shift_info']['start_time'], $b['shift_info']['start_time']);
                                });
                            ?>
                                <div class="schedule-day-card <?php echo $is_today ? 'today-highlight' : ''; ?>">
                                    <div class="schedule-day-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h5>
                                                    <i class="fas fa-calendar-day me-1 text-primary"></i>
                                                    <?php echo formatThaiDate($date); ?>
                                                    <span class="badge bg-secondary ms-1"><?php echo $day_name; ?></span>
                                                    <?php if ($is_today): ?>
                                                        <span class="badge bg-success ms-1">วันนี้</span>
                                                    <?php endif; ?>
                                                    <?php if ($is_holiday): ?>
                                                        <span class="badge bg-warning ms-1">วันหยุด</span>
                                                    <?php endif; ?>
                                                </h5>
                                                <small class="text-muted">
                                                    <?php echo count($shifts_by_type); ?> ประเภทเวร, <?php echo count($day_schedules); ?> คน
                                                </small>
                                            </div>
                                            <?php if ($auth->hasModulePermission('schedules', 'add')): ?>
                                                <a href="?action=add&date=<?php echo $date; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-plus me-1"></i>เพิ่มเวร
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="schedule-day-content">
                                        <?php foreach ($shifts_by_type as $shift_group):
                                            $shift_info = $shift_group['shift_info'];
                                            $doctors = $shift_group['doctors'];
                                        ?>
                                            <div class="shift-group-card" style="border-left: 3px solid <?php echo $shift_info['color']; ?>;">
                                                <div class="shift-group-header">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6>
                                                                <span class="badge me-1" style="background-color: <?php echo $shift_info['color']; ?>; color: white;">
                                                                    <?php echo $shift_info['shift_name']; ?>
                                                                </span>
                                                                <span class="text-muted">
                                                                    <i class="fas fa-clock me-1"></i>
                                                                    <?php echo formatTime($shift_info['start_time']) . ' - ' . formatTime($shift_info['end_time']); ?>
                                                                </span>
                                                            </h6>
                                                            <small class="text-muted">
                                                                <i class="fas fa-users me-1"></i>
                                                                <?php echo count($doctors); ?> คน
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="doctors-list">
                                                    <div class="doctors-names-container">
                                                        <?php foreach ($doctors as $index => $sched): ?>
                                                            <span class="doctor-name-tag"
                                                                  data-bs-toggle="tooltip"
                                                                  data-bs-placement="top"
                                                                  title="<?php echo htmlspecialchars($sched['doctor_code'] . ' - ' . $sched['fullname'] . ($sched['department_name'] ? ' (' . $sched['department_name'] . ')' : '') . ($sched['notes'] ? ' - ' . $sched['notes'] : '')); ?>">

                                                                <span class="doctor-avatar-xs me-1">
                                                                    <?php echo strtoupper(substr($sched['fullname'], 0, 1)); ?>
                                                                </span>

                                                                <span class="doctor-display-name">
                                                                    <?php echo $sched['fullname']; ?>
                                                                </span>

                                                                <?php if ($auth->hasModulePermission('schedules', 'edit') || $auth->hasModulePermission('schedules', 'delete')): ?>
                                                                    <div class="dropdown d-inline">
                                                                        <button class="btn btn-xs btn-link text-muted dropdown-toggle p-0 ms-1" type="button" data-bs-toggle="dropdown" style="font-size: 0.7rem;">
                                                                            <i class="fas fa-ellipsis-h"></i>
                                                                        </button>
                                                                        <ul class="dropdown-menu dropdown-menu-sm">
                                                                            <?php if ($auth->hasModulePermission('schedules', 'edit')): ?>
                                                                                <li>
                                                                                    <a class="dropdown-item" href="?action=edit&id=<?php echo $sched['id']; ?>">
                                                                                        <i class="fas fa-edit me-2"></i>แก้ไข
                                                                                    </a>
                                                                                </li>
                                                                            <?php endif; ?>
                                                                            <?php if ($auth->hasModulePermission('schedules', 'edit') && $auth->hasModulePermission('schedules', 'delete')): ?>
                                                                                <li><hr class="dropdown-divider"></li>
                                                                            <?php endif; ?>
                                                                            <?php if ($auth->hasModulePermission('schedules', 'delete')): ?>
                                                                                <li>
                                                                                    <a class="dropdown-item text-danger" href="#" onclick="confirmDelete(<?php echo $sched['id']; ?>, '<?php echo htmlspecialchars($sched['doctor_code'] . ' - ' . $sched['shift_name'] . ' (' . formatThaiDate($sched['schedule_date']) . ')'); ?>')">
                                                                                        <i class="fas fa-trash me-2"></i>ลบ
                                                                                    </a>
                                                                                </li>
                                                                            <?php endif; ?>
                                                                        </ul>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </span>

                                                            <?php if ($index < count($doctors) - 1): ?>
                                                                <span class="doctor-separator">, </span>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action == 'list'): ?>
            <!-- List View -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            รายการตารางเวร
                        </h5>
                        <!-- Filters -->
                        <div class="d-flex gap-2 align-items-center">
                            <!-- Date Filter -->
                            <input type="date" id="filterDate" class="form-control form-control-sm"
                                   value="<?php echo $_GET['filter_date'] ?? ''; ?>"
                                   onchange="applyFilters()" placeholder="เลือกวันที่">

                            <!-- Doctor Filter -->
                            <select id="filterDoctor" class="form-select form-select-sm" onchange="applyFilters()">
                                <option value="">ทุกแพทย์</option>
                                <?php foreach ($doctors as $doctor): ?>
                                    <option value="<?php echo $doctor['id']; ?>"
                                            <?php echo ($_GET['filter_doctor'] ?? '') == $doctor['id'] ? 'selected' : ''; ?>>
                                        <?php echo $doctor['doctor_code'] . ' - ' . $doctor['first_name'] . ' ' . $doctor['last_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>

                            <!-- Department Filter -->
                            <select id="filterDepartment" class="form-select form-select-sm" onchange="applyFilters()">
                                <option value="">ทุกแผนก</option>
                                <?php
                                $dept_query = "SELECT * FROM departments ORDER BY name";
                                $dept_stmt = $db->prepare($dept_query);
                                $dept_stmt->execute();
                                $departments = $dept_stmt->fetchAll();
                                foreach ($departments as $dept):
                                ?>
                                    <option value="<?php echo $dept['id']; ?>"
                                            <?php echo ($_GET['filter_department'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo $dept['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>

                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllFilters()">
                                <i class="fas fa-times"></i> ล้าง
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    // Get schedules for list view with filters
                    $filter_date = $_GET['filter_date'] ?? null;
                    $filter_doctor = $_GET['filter_doctor'] ?? null;
                    $filter_department = $_GET['filter_department'] ?? null;

                    $list_query = "SELECT s.*, d.fullname, d.doctor_code,
                                          st.name as shift_name, st.start_time, st.end_time, st.color,
                                          dept.name as department_name
                                   FROM schedules s
                                   JOIN doctors d ON s.doctor_id = d.id
                                   JOIN shift_types st ON s.shift_type_id = st.id
                                   LEFT JOIN departments dept ON d.department_id = dept.id";

                    $list_params = [];
                    $where_conditions = [];

                    if ($filter_date) {
                        $where_conditions[] = "s.schedule_date = ?";
                        $list_params[] = $filter_date;
                    }

                    if ($filter_doctor) {
                        $where_conditions[] = "s.doctor_id = ?";
                        $list_params[] = $filter_doctor;
                    }

                    if ($filter_department) {
                        $where_conditions[] = "d.department_id = ?";
                        $list_params[] = $filter_department;
                    }

                    if (!empty($where_conditions)) {
                        $list_query .= " WHERE " . implode(" AND ", $where_conditions);
                    }

                    $list_query .= " ORDER BY s.schedule_date DESC, st.start_time";

                    $list_stmt = $db->prepare($list_query);
                    $list_stmt->execute($list_params);
                    $list_schedules = $list_stmt->fetchAll();

                    // Count total schedules for display
                    $total_count = count($list_schedules);
                    ?>

                    <!-- Filter Summary -->
                    <?php if ($filter_date || $filter_doctor || $filter_department): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-filter me-2"></i>
                            <strong>ตัวกรอง:</strong>
                            <?php if ($filter_date): ?>
                                วันที่ <?php echo formatThaiDate($filter_date); ?>
                            <?php endif; ?>
                            <?php if ($filter_doctor):
                                $selected_doctor = array_filter($doctors, function($d) use ($filter_doctor) { return $d['id'] == $filter_doctor; });
                                $selected_doctor = reset($selected_doctor);
                                if ($selected_doctor): ?>
                                    <?php echo $filter_date ? ', ' : ''; ?>แพทย์ <?php echo $selected_doctor['doctor_code'] . ' - ' . $selected_doctor['first_name'] . ' ' . $selected_doctor['last_name']; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if ($filter_department):
                                $selected_dept = array_filter($departments, function($d) use ($filter_department) { return $d['id'] == $filter_department; });
                                $selected_dept = reset($selected_dept);
                                if ($selected_dept): ?>
                                    <?php echo ($filter_date || $filter_doctor) ? ', ' : ''; ?>แผนก <?php echo $selected_dept['name']; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                            <span class="badge bg-primary ms-2"><?php echo $total_count; ?> รายการ</span>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($list_schedules)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">
                                <?php if ($filter_date || $filter_doctor || $filter_department): ?>
                                    ไม่พบตารางเวรตามเงื่อนไขที่เลือก
                                <?php else: ?>
                                    ไม่พบตารางเวร
                                <?php endif; ?>
                            </p>
                            <?php if ($filter_date || $filter_doctor || $filter_department): ?>
                                <button type="button" class="btn btn-outline-primary" onclick="clearAllFilters()">
                                    <i class="fas fa-list me-1"></i>ดูตารางเวรทั้งหมด
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>วันที่</th>
                                        <th>แพทย์</th>
                                        <th>แผนก</th>
                                        <th>ประเภทเวร</th>
                                        <th>เวลา</th>
                                        <th>สถานะ</th>
                                        <th>หมายเหตุ</th>
                                        <th>จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($list_schedules as $sched): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo formatThaiDate($sched['schedule_date']); ?></strong>
                                                <br><small class="text-muted"><?php echo date('l', strtotime($sched['schedule_date'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="doctor-avatar me-2">
                                                        <?php echo strtoupper(substr($sched['fullname'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo $sched['doctor_code']; ?></strong>
                                                        <br><small><?php echo $sched['fullname']; ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo $sched['department_name'] ?? '-'; ?></td>
                                            <td>
                                                <span class="badge" style="background-color: <?php echo $sched['color']; ?>; color: white;">
                                                    <?php echo $sched['shift_name']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo formatTime($sched['start_time']) . ' - ' . formatTime($sched['end_time']); ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = [
                                                    'scheduled' => 'bg-primary',
                                                    'completed' => 'bg-success',
                                                    'cancelled' => 'bg-danger'
                                                ];
                                                $status_text = [
                                                    'scheduled' => 'กำหนดแล้ว',
                                                    'completed' => 'เสร็จสิ้น',
                                                    'cancelled' => 'ยกเลิก'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $status_class[$sched['status']] ?? 'bg-secondary'; ?>">
                                                    <?php echo $status_text[$sched['status']] ?? $sched['status']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($sched['notes'] ?? '-'); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($auth->hasModulePermission('schedules', 'edit')): ?>
                                                        <a href="?action=edit&id=<?php echo $sched['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($auth->hasModulePermission('schedules', 'delete')): ?>
                                                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete(<?php echo $sched['id']; ?>, '<?php echo htmlspecialchars($sched['doctor_code'] . ' - ' . $sched['shift_name'] . ' (' . formatThaiDate($sched['schedule_date']) . ')'); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action == 'add' || $action == 'edit'): ?>
            <!-- Add/Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-<?php echo $action == 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action == 'add' ? 'เพิ่มตารางเวร' : 'แก้ไขตารางเวร'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="scheduleForm">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label class="form-label">แพทย์ <span class="text-danger">*</span></label>
                                <select class="form-select" name="doctor_id" id="doctorSelect" required>
                                    <option value="">เลือกแพทย์</option>
                                    <?php foreach ($doctors as $doctor): ?>
                                        <option value="<?php echo $doctor['id']; ?>" <?php echo ($schedule['doctor_id'] ?? '') == $doctor['id'] ? 'selected' : ''; ?>>
                                            <?php echo $doctor['doctor_code'] . ' - ' . $doctor['fullname']; ?>
                                            <?php if ($doctor['department_name']): ?>
                                                (<?php echo $doctor['department_name']; ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- ช่วงเวลาการสร้างตารางเวร -->
                            <div class="col-12 mb-4">
                                <label class="form-label">ช่วงเวลาการสร้างตารางเวร <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="schedule_period"
                                                           value="weekly" id="period_weekly" checked onchange="updatePeriodOptions()">
                                                    <label class="form-check-label fw-bold" for="period_weekly">
                                                        <i class="fas fa-calendar-week me-2"></i>รายสัปดาห์
                                                    </label>
                                                </div>
                                                <small class="text-muted">สร้างตารางเวรสำหรับ 1 สัปดาห์</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="schedule_period"
                                                           value="monthly" id="period_monthly" onchange="updatePeriodOptions()">
                                                    <label class="form-check-label fw-bold" for="period_monthly">
                                                        <i class="fas fa-calendar me-2"></i>รายเดือน
                                                    </label>
                                                </div>
                                                <small class="text-muted">สร้างตารางเวรสำหรับ 1 เดือน</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- วันที่เริ่มต้น -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">วันที่เริ่มต้น <span class="text-danger">*</span></label>
                                        <input type="date" class="form-select" name="start_date" id="startDate"
                                               value="<?php echo date('Y-m-d'); ?>" required onchange="updateDateRange()">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">วันที่สิ้นสุด</label>
                                        <input type="date" class="form-select" name="end_date" id="endDate" readonly>
                                        <small class="text-muted" id="dateRangeInfo">จะคำนวณอัตโนมัติตามช่วงเวลาที่เลือก</small>
                                    </div>
                                </div>
                            </div>

                            <!-- วันในสัปดาห์และประเภทเวร -->
                            <div class="col-12 mb-4">
                                <label class="form-label">เลือกวันและประเภทเวร <span class="text-danger">*</span></label>
                                <div class="row">
                                    <?php
                                    $days = [
                                        'monday' => 'จันทร์',
                                        'tuesday' => 'อังคาร',
                                        'wednesday' => 'พุธ',
                                        'thursday' => 'พฤหัสบดี',
                                        'friday' => 'ศุกร์',
                                        'saturday' => 'เสาร์',
                                        'sunday' => 'อาทิตย์'
                                    ];
                                    foreach ($days as $day_en => $day_th):
                                    ?>
                                        <div class="col-lg-3 col-md-4 col-6 mb-3">
                                            <div class="card day-card h-100" id="card_<?php echo $day_en; ?>">
                                                <div class="card-header py-2">
                                                    <div class="form-check mb-0">
                                                        <input class="form-check-input day-checkbox" type="checkbox"
                                                               name="selected_days[]" value="<?php echo $day_en; ?>"
                                                               id="day_<?php echo $day_en; ?>" onchange="toggleDayShifts('<?php echo $day_en; ?>')">
                                                        <label class="form-check-label fw-bold" for="day_<?php echo $day_en; ?>">
                                                            <?php echo $day_th; ?>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="card-body py-2" id="shifts_<?php echo $day_en; ?>" style="display: none;">
                                                    <small class="text-muted d-block mb-2">เลือกประเภทเวร:</small>
                                                    <?php foreach ($shift_types as $shift): ?>
                                                        <div class="form-check form-check-sm mb-1">
                                                            <input class="form-check-input shift-checkbox" type="checkbox"
                                                                   name="shifts[<?php echo $day_en; ?>][]"
                                                                   value="<?php echo $shift['id']; ?>"
                                                                   id="shift_<?php echo $day_en; ?>_<?php echo $shift['id']; ?>"
                                                                   disabled>
                                                            <label class="form-check-label small" for="shift_<?php echo $day_en; ?>_<?php echo $shift['id']; ?>">
                                                                <strong><?php echo $shift['name']; ?></strong>
                                                                <br><small class="text-muted">
                                                                    <?php echo formatTime($shift['start_time']) . ' - ' . formatTime($shift['end_time']); ?>
                                                                </small>
                                                            </label>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- ปุ่มลัด -->
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <strong>เลือกวัน:</strong>
                                            <div class="btn-group-sm d-flex flex-wrap gap-1">
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllDays()">
                                                    <i class="fas fa-check-double me-1"></i>ทุกวัน
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectWeekdays()">
                                                    <i class="fas fa-briefcase me-1"></i>จ-ศ
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="selectWeekends()">
                                                    <i class="fas fa-calendar-week me-1"></i>ส-อา
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllDays()">
                                                    <i class="fas fa-times me-1"></i>ล้าง
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-12 mb-2">
                                            <strong>เลือกเวร:</strong>
                                            <div class="row">
                                                <div class="col-12 mb-2">
                                                    <div class="btn-group-sm d-flex flex-wrap gap-1">
                                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="selectAllShifts()">
                                                            <i class="fas fa-check-circle me-1"></i>ทุกเวร
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllShifts()">
                                                            <i class="fas fa-times me-1"></i>ล้างทั้งหมด
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <small class="text-muted d-block mb-1">เลือกประเภทเวร (คลิกเพื่อเพิ่ม / Shift+คลิกเพื่อแทนที่):</small>
                                                    <div class="btn-group-sm d-flex flex-wrap gap-1">
                                                        <?php foreach ($shift_types as $shift): ?>
                                                            <button type="button" class="btn btn-outline-info btn-sm shift-type-btn"
                                                                    onclick="handleShiftSelection(event, '<?php echo $shift['id']; ?>')"
                                                                    style="background-color: <?php echo $shift['color']; ?>15; border-color: <?php echo $shift['color']; ?>; color: <?php echo $shift['color']; ?>;"
                                                                    title="<?php echo $shift['name']; ?> (<?php echo formatTime($shift['start_time']) . ' - ' . formatTime($shift['end_time']); ?>)">
                                                                <i class="fas fa-clock me-1"></i><?php echo $shift['name']; ?>
                                                            </button>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- สรุปการเลือก -->
                            <div class="col-12 mb-3">
                                <div class="card bg-light">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list-check me-2"></i>สรุปการเลือก
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div id="selectionSummary" class="text-muted">
                                            กรุณาเลือกวันและประเภทเวรที่ต้องการ
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">หมายเหตุ</label>
                                <textarea class="form-control" name="notes" rows="2" placeholder="หมายเหตุเพิ่มเติม (ถ้ามี)"><?php echo htmlspecialchars($schedule['notes'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="schedule.php" class="btn btn-secondary">ยกเลิก</a>
                            <button type="submit" name="<?php echo $action == 'add' ? 'add_schedule' : 'update_schedule'; ?>" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?php echo $action == 'add' ? 'เพิ่มตารางเวร' : 'บันทึกการแก้ไข'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณต้องการลบตารางเวร <strong id="scheduleInfo"></strong> หรือไม่?</p>
                    <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถยกเลิกได้</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <form method="POST" style="display: inline;" id="deleteForm">
                        <input type="hidden" id="deleteId" name="schedule_id" value="">
                        <button type="submit" name="delete_schedule" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>ลบตารางเวร
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, info) {
            document.getElementById('deleteId').value = id;
            document.getElementById('scheduleInfo').textContent = info;

            // แสดง modal
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // ตรวจสอบก่อนส่งฟอร์มลบ
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            const scheduleInfo = document.getElementById('scheduleInfo').textContent;
            if (!confirm('คุณแน่ใจหรือไม่ที่จะลบตารางเวร ' + scheduleInfo + '?')) {
                e.preventDefault();
                return false;
            }
        });

        // ฟังก์ชันใช้ตัวกรองทั้งหมด
        function applyFilters() {
            const date = document.getElementById('filterDate').value;
            const doctor = document.getElementById('filterDoctor').value;
            const department = document.getElementById('filterDepartment').value;

            let url = '?action=list';
            const params = [];

            if (date) params.push('filter_date=' + date);
            if (doctor) params.push('filter_doctor=' + doctor);
            if (department) params.push('filter_department=' + department);

            if (params.length > 0) {
                url += '&' + params.join('&');
            }

            window.location.href = url;
        }

        // ฟังก์ชันล้างตัวกรองทั้งหมด
        function clearAllFilters() {
            window.location.href = '?action=list';
        }

        // ฟังก์ชันกรองตามวันที่ (เก็บไว้เพื่อ backward compatibility)
        function filterByDate(date) {
            document.getElementById('filterDate').value = date;
            applyFilters();
        }

        // ฟังก์ชันเปลี่ยนเดือนในปฏิทิน
        function changeCalendarMonth(monthValue) {
            if (monthValue) {
                // แปลง YYYY-MM เป็น YYYY-MM-01
                const date = monthValue + '-01';

                // เก็บ filters ปัจจุบัน
                const doctor = document.getElementById('calendarFilterDoctor')?.value || '';
                const department = document.getElementById('calendarFilterDepartment')?.value || '';

                let url = '?action=calendar&date=' + date;
                if (doctor) url += '&calendar_doctor=' + doctor;
                if (department) url += '&calendar_department=' + department;

                window.location.href = url;
            }
        }

        // ฟังก์ชันใช้ตัวกรองในปฏิทิน
        function applyCalendarFilters() {
            const doctor = document.getElementById('calendarFilterDoctor').value;
            const department = document.getElementById('calendarFilterDepartment').value;
            const currentDate = new URLSearchParams(window.location.search).get('date') || '<?php echo date('Y-m-d'); ?>';

            let url = '?action=calendar&date=' + currentDate;
            if (doctor) url += '&calendar_doctor=' + doctor;
            if (department) url += '&calendar_department=' + department;

            window.location.href = url;
        }

        // ฟังก์ชันล้างตัวกรองในปฏิทิน
        function clearCalendarFilters() {
            const currentDate = new URLSearchParams(window.location.search).get('date') || '<?php echo date('Y-m-d'); ?>';
            window.location.href = '?action=calendar&date=' + currentDate;
        }

        // ฟังก์ชันสำหรับจัดการ checkbox วันและประเภทเวร
        function toggleDayShifts(dayName) {
            const dayCheckbox = document.getElementById('day_' + dayName);
            const shiftsContainer = document.getElementById('shifts_' + dayName);
            const shiftCheckboxes = shiftsContainer.querySelectorAll('.shift-checkbox');
            const dayCard = document.getElementById('card_' + dayName);

            if (dayCheckbox.checked) {
                shiftsContainer.style.display = 'block';
                dayCard.classList.add('border-primary');
                // เปิดใช้งาน shift checkboxes
                shiftCheckboxes.forEach(cb => cb.disabled = false);
            } else {
                shiftsContainer.style.display = 'none';
                dayCard.classList.remove('border-primary');
                // ปิดใช้งานและยกเลิกการเลือก shift checkboxes
                shiftCheckboxes.forEach(cb => {
                    cb.disabled = true;
                    cb.checked = false;
                });
            }
            updateSelectionSummary();
        }

        // ฟังก์ชันอัปเดตช่วงวันที่
        function updateDateRange() {
            const startDate = document.getElementById('startDate').value;
            const period = document.querySelector('input[name="schedule_period"]:checked')?.value;
            const endDateInput = document.getElementById('endDate');
            const dateRangeInfo = document.getElementById('dateRangeInfo');

            if (startDate && period) {
                const start = new Date(startDate);
                let end = new Date(start);

                if (period === 'weekly') {
                    end.setDate(start.getDate() + 6);
                    dateRangeInfo.textContent = 'สิ้นสุด: ' + end.toLocaleDateString('th-TH');
                } else if (period === 'monthly') {
                    end = new Date(start.getFullYear(), start.getMonth() + 1, 0);
                    dateRangeInfo.textContent = 'สิ้นสุด: ' + end.toLocaleDateString('th-TH');
                }

                endDateInput.value = end.toISOString().split('T')[0];
            }
        }

        // ฟังก์ชันอัปเดตตัวเลือกตามช่วงเวลา
        function updatePeriodOptions() {
            updateDateRange();
            updateSelectionSummary();
        }

        // ฟังก์ชันเลือกประเภทเวรทั้งหมด
        function selectAllShifts() {
            const selectedDays = document.querySelectorAll('.day-checkbox:checked');
            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const shiftCheckboxes = document.querySelectorAll(`input[name="shifts[${dayName}][]"]`);
                shiftCheckboxes.forEach(cb => cb.checked = true);
            });
            updateSelectionSummary();
        }

        // ฟังก์ชันเลือกเวรตามประเภท
        function selectShiftsByType(shiftTypeIds) {
            const selectedDays = document.querySelectorAll('.day-checkbox:checked');
            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const shiftCheckboxes = document.querySelectorAll(`input[name="shifts[${dayName}][]"]`);
                shiftCheckboxes.forEach(cb => {
                    if (shiftTypeIds.includes(cb.value)) {
                        cb.checked = true;
                    } else {
                        cb.checked = false;
                    }
                });
            });
            updateSelectionSummary();
        }

        // ฟังก์ชันเลือกเวรเช้า (ดึงจากฐานข้อมูล)
        function selectMorningShifts() {
            // ดึง shift types ที่มีคำว่า "เช้า" หรือเวลาเริ่มต้นเป็น 06:00-10:00
            const morningShiftIds = [];
            const shiftCheckboxes = document.querySelectorAll('.shift-checkbox');
            shiftCheckboxes.forEach(cb => {
                const label = cb.nextElementSibling.textContent;
                if (label.includes('เช้า') ||
                    label.includes('08:00') ||
                    label.includes('06:00') ||
                    label.includes('07:00') ||
                    label.includes('09:00')) {
                    if (!morningShiftIds.includes(cb.value)) {
                        morningShiftIds.push(cb.value);
                    }
                }
            });
            selectShiftsByType(morningShiftIds);
        }

        // ฟังก์ชันเลือกเวรบ่าย/ดึก (ดึงจากฐานข้อมูล)
        function selectEveningShifts() {
            // ดึง shift types ที่มีคำว่า "บ่าย", "ดึก", "พิเศษ" หรือเวลาเริ่มต้นเป็น 16:00 ขึ้นไป
            const eveningShiftIds = [];
            const shiftCheckboxes = document.querySelectorAll('.shift-checkbox');
            shiftCheckboxes.forEach(cb => {
                const label = cb.nextElementSibling.textContent;
                if (label.includes('บ่าย') ||
                    label.includes('ดึก') ||
                    label.includes('พิเศษ') ||
                    label.includes('16:00') ||
                    label.includes('18:00') ||
                    label.includes('00:00') ||
                    label.includes('22:00')) {
                    if (!eveningShiftIds.includes(cb.value)) {
                        eveningShiftIds.push(cb.value);
                    }
                }
            });
            selectShiftsByType(eveningShiftIds);
        }

        // ฟังก์ชันเลือกประเภทเวรเฉพาะ (เพิ่มเติม ไม่ลบของเดิม)
        function selectSpecificShift(shiftTypeId) {
            const selectedDays = document.querySelectorAll('.day-checkbox:checked');
            if (selectedDays.length === 0) {
                alert('กรุณาเลือกวันก่อน');
                return;
            }

            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const shiftCheckbox = document.querySelector(`input[name="shifts[${dayName}][]"][value="${shiftTypeId}"]`);
                if (shiftCheckbox) {
                    shiftCheckbox.checked = true;
                }
            });
            updateSelectionSummary();
        }

        // ฟังก์ชันเลือกประเภทเวรเฉพาะ (แทนที่ของเดิม)
        function selectOnlySpecificShift(shiftTypeId) {
            const selectedDays = document.querySelectorAll('.day-checkbox:checked');
            if (selectedDays.length === 0) {
                alert('กรุณาเลือกวันก่อน');
                return;
            }

            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const shiftCheckboxes = document.querySelectorAll(`input[name="shifts[${dayName}][]"]`);
                shiftCheckboxes.forEach(cb => {
                    if (cb.value === shiftTypeId) {
                        cb.checked = true;
                    } else {
                        cb.checked = false;
                    }
                });
            });
            updateSelectionSummary();
        }

        // ฟังก์ชันจัดการการเลือกประเภทเวร (เพิ่ม หรือ แทนที่)
        function handleShiftSelection(event, shiftTypeId) {
            const selectedDays = document.querySelectorAll('.day-checkbox:checked');
            if (selectedDays.length === 0) {
                alert('กรุณาเลือกวันก่อน');
                return;
            }

            // ตรวจสอบว่ากด Shift key หรือไม่
            const isReplaceMode = event.shiftKey;

            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const shiftCheckboxes = document.querySelectorAll(`input[name="shifts[${dayName}][]"]`);

                if (isReplaceMode) {
                    // โหมดแทนที่: ยกเลิกทั้งหมดแล้วเลือกเฉพาะตัวนี้
                    shiftCheckboxes.forEach(cb => {
                        if (cb.value === shiftTypeId) {
                            cb.checked = true;
                        } else {
                            cb.checked = false;
                        }
                    });
                } else {
                    // โหมดเพิ่ม: toggle การเลือก
                    const targetCheckbox = document.querySelector(`input[name="shifts[${dayName}][]"][value="${shiftTypeId}"]`);
                    if (targetCheckbox) {
                        targetCheckbox.checked = !targetCheckbox.checked;
                    }
                }
            });

            updateSelectionSummary();

            // แสดงข้อความแนะนำ
            if (isReplaceMode) {
                showTooltip(event.target, 'แทนที่ด้วย: ' + event.target.textContent.trim());
            } else {
                const targetCheckbox = document.querySelector(`input[name="shifts[${selectedDays[0].value}][]"][value="${shiftTypeId}"]`);
                if (targetCheckbox && targetCheckbox.checked) {
                    showTooltip(event.target, 'เพิ่ม: ' + event.target.textContent.trim());
                } else {
                    showTooltip(event.target, 'ยกเลิก: ' + event.target.textContent.trim());
                }
            }
        }

        // ฟังก์ชันแสดง tooltip ชั่วคราว
        function showTooltip(element, message) {
            // ลบ tooltip เก่า (ถ้ามี)
            const existingTooltip = document.querySelector('.temp-tooltip');
            if (existingTooltip) {
                existingTooltip.remove();
            }

            // สร้าง tooltip ใหม่
            const tooltip = document.createElement('div');
            tooltip.className = 'temp-tooltip';
            tooltip.textContent = message;
            tooltip.style.cssText = `
                position: absolute;
                background: #333;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.7rem;
                z-index: 1000;
                pointer-events: none;
                white-space: nowrap;
            `;

            document.body.appendChild(tooltip);

            // วางตำแหน่ง tooltip
            const rect = element.getBoundingClientRect();
            tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';

            // ลบ tooltip หลัง 1.5 วินาที
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 1500);
        }

        // ฟังก์ชันล้างการเลือกประเภทเวรทั้งหมด
        function clearAllShifts() {
            const shiftCheckboxes = document.querySelectorAll('.shift-checkbox');
            shiftCheckboxes.forEach(cb => cb.checked = false);
            updateSelectionSummary();
        }

        // ฟังก์ชันอัปเดตสรุปการเลือก
        function updateSelectionSummary() {
            const summaryDiv = document.getElementById('selectionSummary');
            if (!summaryDiv) return;

            const selectedDays = document.querySelectorAll('.day-checkbox:checked');
            const period = document.querySelector('input[name="schedule_period"]:checked')?.value;

            // อัปเดต visual indicators สำหรับ day cards
            updateDayCardIndicators();

            if (selectedDays.length === 0) {
                summaryDiv.innerHTML = '<span class="text-muted">กรุณาเลือกวันและประเภทเวรที่ต้องการ</span>';
                return;
            }

            let summary = '<div class="row">';
            let totalSchedules = 0;
            let hasAnyShifts = false;

            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const dayText = dayCheckbox.nextElementSibling.textContent;
                const selectedShifts = document.querySelectorAll(`input[name="shifts[${dayName}][]"]:checked`);

                summary += `<div class="col-md-6 mb-2">`;
                summary += `<div class="d-flex align-items-center">`;
                summary += `<strong class="me-2">${dayText}:</strong>`;

                if (selectedShifts.length > 0) {
                    hasAnyShifts = true;
                    const shiftDetails = Array.from(selectedShifts).map(cb => {
                        const label = cb.nextElementSibling.querySelector('strong').textContent;
                        const timeText = cb.nextElementSibling.querySelector('small').textContent;
                        return `<span class="badge bg-primary me-1" style="font-size: 0.7rem;">${label}</span>`;
                    });

                    summary += shiftDetails.join('');
                    summary += `<small class="text-muted ms-1">(${selectedShifts.length} เวร)</small>`;

                    // คำนวณจำนวนตารางเวรที่จะสร้าง
                    if (period === 'weekly') {
                        totalSchedules += selectedShifts.length;
                    } else if (period === 'monthly') {
                        // ประมาณการ 4 สัปดาห์ต่อเดือน
                        totalSchedules += selectedShifts.length * 4;
                    }
                } else {
                    summary += `<span class="text-muted">ยังไม่ได้เลือกเวร</span>`;
                }

                summary += `</div></div>`;
            });

            summary += '</div>';

            if (hasAnyShifts) {
                summary += `<div class="mt-3 p-2 bg-light rounded">`;
                summary += `<div class="row">`;
                summary += `<div class="col-md-6">`;
                summary += `<small class="text-info d-block">`;
                summary += `<i class="fas fa-info-circle me-1"></i>`;
                summary += `<strong>จำนวนตารางเวรที่จะสร้าง:</strong> ประมาณ <span class="text-primary">${totalSchedules}</span> รายการ`;
                if (period === 'monthly') {
                    summary += ` (ขึ้นอยู่กับจำนวนวันในเดือน)`;
                }
                summary += `</small>`;
                summary += `</div>`;
                summary += `<div class="col-md-6">`;
                summary += `<small class="text-muted">`;
                summary += `<i class="fas fa-lightbulb me-1"></i>`;
                summary += `<strong>เคล็ดลับ:</strong> คลิกปุ่มเวรเพื่อเพิ่ม, Shift+คลิกเพื่อแทนที่`;
                summary += `</small>`;
                summary += `</div>`;
                summary += `</div>`;
                summary += `</div>`;
            } else {
                summary += `<div class="mt-2">`;
                summary += `<small class="text-warning">`;
                summary += `<i class="fas fa-exclamation-triangle me-1"></i>`;
                summary += `กรุณาเลือกประเภทเวรสำหรับวันที่เลือกไว้`;
                summary += `</small>`;
                summary += `</div>`;
            }

            summaryDiv.innerHTML = summary;
        }

        // ฟังก์ชันอัปเดต visual indicators สำหรับ day cards
        function updateDayCardIndicators() {
            const selectedDays = document.querySelectorAll('.day-checkbox:checked');

            // รีเซ็ต indicators ทั้งหมด
            document.querySelectorAll('.day-card').forEach(card => {
                card.classList.remove('has-shifts');
                const counter = card.querySelector('.shift-counter');
                if (counter) counter.remove();
            });

            selectedDays.forEach(dayCheckbox => {
                const dayName = dayCheckbox.value;
                const dayCard = document.getElementById('card_' + dayName);
                const selectedShifts = document.querySelectorAll(`input[name="shifts[${dayName}][]"]:checked`);

                if (selectedShifts.length > 0) {
                    dayCard.classList.add('has-shifts');

                    // เพิ่ม counter badge
                    const counter = document.createElement('span');
                    counter.className = 'shift-counter';
                    counter.textContent = selectedShifts.length;
                    dayCard.style.position = 'relative';
                    dayCard.appendChild(counter);
                }
            });
        }

        // เพิ่มปุ่มลัดสำหรับวันที่ที่ใช้บ่อย
        function addQuickDateButtons() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const nextWeek = new Date(today);
            nextWeek.setDate(nextWeek.getDate() + 7);

            const quickDates = [
                { label: 'วันนี้', date: today.toISOString().split('T')[0] },
                { label: 'พรุ่งนี้', date: tomorrow.toISOString().split('T')[0] },
                { label: 'สัปดาห์หน้า', date: nextWeek.toISOString().split('T')[0] }
            ];

            // เพิ่มปุ่มลัดถ้าอยู่ในหน้า list
            if (window.location.search.includes('action=list')) {
                const filterContainer = document.querySelector('.card-header .d-flex');
                if (filterContainer) {
                    const quickButtonsDiv = document.createElement('div');
                    quickButtonsDiv.className = 'btn-group btn-group-sm ms-2';

                    quickDates.forEach(item => {
                        const btn = document.createElement('button');
                        btn.type = 'button';
                        btn.className = 'btn btn-outline-info btn-sm';
                        btn.textContent = item.label;
                        btn.onclick = () => filterByDate(item.date);
                        quickButtonsDiv.appendChild(btn);
                    });

                    filterContainer.appendChild(quickButtonsDiv);
                }
            }
        }

        // ฟังก์ชันจัดการโหมดการเลือกวันที่
        function handleDateModeChange() {
            const singleDate = document.getElementById('single_date');
            const multipleDates = document.getElementById('multiple_dates');
            const weeklyPattern = document.getElementById('weekly_pattern');
            const monthlyPattern = document.getElementById('monthly_pattern');

            const singleSection = document.getElementById('singleDateSection');
            const multipleSection = document.getElementById('multipleDatesSection');
            const weeklySection = document.getElementById('weeklyPatternSection');
            const monthlySection = document.getElementById('monthlyPatternSection');
            const previewSection = document.getElementById('datePreview');

            // ซ่อนทุกส่วน
            singleSection.style.display = 'none';
            multipleSection.style.display = 'none';
            weeklySection.style.display = 'none';
            monthlySection.style.display = 'none';
            previewSection.style.display = 'none';

            // แสดงส่วนที่เลือก
            if (singleDate.checked) {
                singleSection.style.display = 'block';
                document.getElementById('scheduleDate').required = true;
            } else if (multipleDates.checked) {
                multipleSection.style.display = 'block';
                previewSection.style.display = 'block';
                document.getElementById('scheduleDate').required = false;
                updateMultipleDatesPreview();
            } else if (weeklyPattern.checked) {
                weeklySection.style.display = 'block';
                previewSection.style.display = 'block';
                document.getElementById('scheduleDate').required = false;
                updateWeeklyPatternPreview();
            } else if (monthlyPattern.checked) {
                monthlySection.style.display = 'block';
                previewSection.style.display = 'block';
                document.getElementById('scheduleDate').required = false;
                updateMonthlyPatternPreview();
            }
        }

        // ฟังก์ชันตั้งค่าวันที่ด่วน
        function setQuickDate(type) {
            const dateInput = document.getElementById('scheduleDate');
            const today = new Date();
            let targetDate = new Date();

            switch(type) {
                case 'today':
                    targetDate = today;
                    break;
                case 'tomorrow':
                    targetDate.setDate(today.getDate() + 1);
                    break;
                case 'next_week':
                    targetDate.setDate(today.getDate() + 7);
                    break;
                case 'next_month':
                    targetDate.setMonth(today.getMonth() + 1);
                    break;
            }

            dateInput.value = targetDate.toISOString().split('T')[0];
            updateDateInfo();
        }

        // ฟังก์ชันอัปเดตข้อมูลวันที่
        function updateDateInfo() {
            const dateInput = document.getElementById('scheduleDate');
            const dateInfo = document.getElementById('dateInfo');

            if (dateInput.value) {
                const selectedDate = new Date(dateInput.value);
                const dayNames = ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'];
                const dayName = dayNames[selectedDate.getDay()];

                dateInfo.textContent = `วัน${dayName}`;

                // เช็คว่าเป็นวันหยุดหรือไม่
                if (selectedDate.getDay() === 0 || selectedDate.getDay() === 6) {
                    dateInfo.innerHTML += ' <span class="text-warning">(วันหยุด)</span>';
                }
            }
        }

        // ฟังก์ชันเลือกวันทั้งหมด
        function selectAllDays() {
            const checkboxes = document.querySelectorAll('input[name="selected_days[]"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateWeeklyPatternPreview();
        }

        // ฟังก์ชันเลือกวันจันทร์-ศุกร์
        function selectWeekdays() {
            const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
            const checkboxes = document.querySelectorAll('input[name="selected_days[]"]');

            checkboxes.forEach(cb => {
                cb.checked = weekdays.includes(cb.value);
            });
            updateWeeklyPatternPreview();
        }

        // ฟังก์ชันเลือกวันเสาร์-อาทิตย์
        function selectWeekends() {
            const weekends = ['saturday', 'sunday'];
            const checkboxes = document.querySelectorAll('input[name="selected_days[]"]');

            checkboxes.forEach(cb => {
                cb.checked = weekends.includes(cb.value);
            });
            updateWeeklyPatternPreview();
        }

        // ฟังก์ชันล้างการเลือกวันทั้งหมด
        function clearAllDays() {
            const checkboxes = document.querySelectorAll('input[name="selected_days[]"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateWeeklyPatternPreview();
        }

        // ฟังก์ชันอัปเดตตัวอย่างหลายวัน
        function updateMultipleDatesPreview() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const previewContent = document.getElementById('previewContent');
            const scheduleCount = document.getElementById('scheduleCount');

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const dates = [];

                for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                    dates.push(new Date(d));
                }

                const dayNames = ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'];
                const dateStrings = dates.map(date => {
                    const dayName = dayNames[date.getDay()];
                    const dateStr = date.toLocaleDateString('th-TH');
                    return `${dateStr} (${dayName})`;
                });

                previewContent.innerHTML = dateStrings.slice(0, 10).join(', ');
                if (dates.length > 10) {
                    previewContent.innerHTML += ` และอีก ${dates.length - 10} วัน...`;
                }

                scheduleCount.textContent = dates.length;
            }
        }

        // ฟังก์ชันอัปเดตตัวอย่างรูปแบบสัปดาห์
        function updateWeeklyPatternPreview() {
            const startDate = document.getElementById('patternStartDate').value;
            const weeksCount = parseInt(document.getElementById('weeksCount').value);
            const selectedDays = Array.from(document.querySelectorAll('input[name="selected_days[]"]:checked')).map(cb => cb.value);
            const previewContent = document.getElementById('previewContent');
            const scheduleCount = document.getElementById('scheduleCount');

            if (startDate && selectedDays.length > 0) {
                const start = new Date(startDate);
                const dates = [];
                const dayMapping = {
                    'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
                    'thursday': 4, 'friday': 5, 'saturday': 6
                };
                const dayNames = ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'];

                for (let week = 0; week < weeksCount; week++) {
                    selectedDays.forEach(day => {
                        const targetDay = dayMapping[day];
                        const date = new Date(start);
                        date.setDate(start.getDate() + (week * 7) + (targetDay - start.getDay() + 7) % 7);
                        dates.push(date);
                    });
                }

                dates.sort((a, b) => a - b);

                const dateStrings = dates.map(date => {
                    const dayName = dayNames[date.getDay()];
                    const dateStr = date.toLocaleDateString('th-TH');
                    return `${dateStr} (${dayName})`;
                });

                previewContent.innerHTML = dateStrings.slice(0, 10).join(', ');
                if (dates.length > 10) {
                    previewContent.innerHTML += ` และอีก ${dates.length - 10} วัน...`;
                }

                scheduleCount.textContent = dates.length;
            } else {
                previewContent.innerHTML = 'กรุณาเลือกวันในสัปดาห์';
                scheduleCount.textContent = '0';
            }
        }

        // ฟังก์ชันอัปเดตตัวอย่างรูปแบบรายเดือน
        function updateMonthlyPatternPreview() {
            const targetMonth = document.getElementById('targetMonth').value;
            const patternType = document.getElementById('monthlyPatternType').value;
            const excludeHolidays = document.getElementById('excludeHolidays').checked;
            const selectedDays = Array.from(document.querySelectorAll('input[name="monthly_selected_days[]"]:checked')).map(cb => cb.value);
            const previewContent = document.getElementById('previewContent');
            const scheduleCount = document.getElementById('scheduleCount');

            if (targetMonth) {
                const [year, month] = targetMonth.split('-');
                const monthStart = new Date(year, month - 1, 1);
                const monthEnd = new Date(year, month, 0);
                const dates = [];
                const dayMapping = {
                    'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
                    'thursday': 4, 'friday': 5, 'saturday': 6
                };
                const dayNames = ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'];

                for (let date = new Date(monthStart); date <= monthEnd; date.setDate(date.getDate() + 1)) {
                    const dayOfWeek = date.getDay();
                    let shouldInclude = false;

                    switch (patternType) {
                        case 'all_days':
                            shouldInclude = true;
                            break;
                        case 'weekdays_only':
                            shouldInclude = (dayOfWeek >= 1 && dayOfWeek <= 5);
                            break;
                        case 'weekends_only':
                            shouldInclude = (dayOfWeek === 0 || dayOfWeek === 6);
                            break;
                        case 'custom_days':
                            shouldInclude = selectedDays.some(day => dayMapping[day] === dayOfWeek);
                            break;
                    }

                    if (shouldInclude && excludeHolidays) {
                        if (patternType !== 'weekends_only' && (dayOfWeek === 0 || dayOfWeek === 6)) {
                            shouldInclude = false;
                        }
                    }

                    if (shouldInclude) {
                        dates.push(new Date(date));
                    }
                }

                const dateStrings = dates.map(date => {
                    const dayName = dayNames[date.getDay()];
                    const dateStr = date.toLocaleDateString('th-TH');
                    return `${dateStr} (${dayName})`;
                });

                previewContent.innerHTML = dateStrings.slice(0, 15).join(', ');
                if (dates.length > 15) {
                    previewContent.innerHTML += ` และอีก ${dates.length - 15} วัน...`;
                }

                scheduleCount.textContent = dates.length;
            } else {
                previewContent.innerHTML = 'กรุณาเลือกเดือน/ปี';
                scheduleCount.textContent = '0';
            }
        }

        // ฟังก์ชันจัดการการเปลี่ยนรูปแบบรายเดือน
        function handleMonthlyPatternChange() {
            const patternType = document.getElementById('monthlyPatternType').value;
            const customDaysSection = document.getElementById('customDaysSection');

            if (patternType === 'custom_days') {
                customDaysSection.style.display = 'block';
            } else {
                customDaysSection.style.display = 'none';
            }

            updateMonthlyPatternPreview();
        }

        // ฟังก์ชันเลือกวันทั้งหมดสำหรับรายเดือน
        function selectAllMonthlyDays() {
            const checkboxes = document.querySelectorAll('input[name="monthly_selected_days[]"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateMonthlyPatternPreview();
        }

        // ฟังก์ชันเลือกวันจันทร์-ศุกร์สำหรับรายเดือน
        function selectMonthlyWeekdays() {
            const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
            const checkboxes = document.querySelectorAll('input[name="monthly_selected_days[]"]');

            checkboxes.forEach(cb => {
                cb.checked = weekdays.includes(cb.value);
            });
            updateMonthlyPatternPreview();
        }

        // ฟังก์ชันเลือกวันเสาร์-อาทิตย์สำหรับรายเดือน
        function selectMonthlyWeekends() {
            const weekends = ['saturday', 'sunday'];
            const checkboxes = document.querySelectorAll('input[name="monthly_selected_days[]"]');

            checkboxes.forEach(cb => {
                cb.checked = weekends.includes(cb.value);
            });
            updateMonthlyPatternPreview();
        }

        // ฟังก์ชันล้างการเลือกวันทั้งหมดสำหรับรายเดือน
        function clearAllMonthlyDays() {
            const checkboxes = document.querySelectorAll('input[name="monthly_selected_days[]"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateMonthlyPatternPreview();
        }

        // ฟังก์ชันเปลี่ยนเดือนในปฏิทิน
        function changeCalendarMonth(monthValue) {
            const [year, month] = monthValue.split('-');
            const newDate = year + '-' + month + '-01';
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('action', 'calendar');
            currentUrl.searchParams.set('view_mode', 'month');
            currentUrl.searchParams.set('date', newDate);
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันจัดการการค้นหาในปฏิทิน
        function handleCalendarSearch(event) {
            if (event.key === 'Enter') {
                applyCalendarSearch();
            }
        }

        // ฟังก์ชันใช้การค้นหาปฏิทิน
        function applyCalendarSearch() {
            const search = document.getElementById('calendarSearch').value.trim();
            const currentUrl = new URL(window.location);

            if (search) {
                currentUrl.searchParams.set('calendar_search', search);
            } else {
                currentUrl.searchParams.delete('calendar_search');
            }

            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันล้างการค้นหาปฏิทิน
        function clearCalendarSearch() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('calendar_search');
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันใช้ตัวกรองปฏิทิน
        function applyCalendarFilters() {
            const doctor = document.getElementById('calendarFilterDoctor').value;
            const department = document.getElementById('calendarFilterDepartment').value;
            const currentUrl = new URL(window.location);

            if (doctor) {
                currentUrl.searchParams.set('calendar_doctor', doctor);
            } else {
                currentUrl.searchParams.delete('calendar_doctor');
            }

            if (department) {
                currentUrl.searchParams.set('calendar_department', department);
            } else {
                currentUrl.searchParams.delete('calendar_department');
            }

            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันล้างตัวกรองปฏิทิน
        function clearCalendarFilters() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('calendar_doctor');
            currentUrl.searchParams.delete('calendar_department');
            currentUrl.searchParams.delete('calendar_search');
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันใช้ตัวกรองรายการ
        function applyFilters() {
            const date = document.getElementById('filterDate').value;
            const doctor = document.getElementById('filterDoctor').value;
            const department = document.getElementById('filterDepartment').value;
            const currentUrl = new URL(window.location);

            if (date) {
                currentUrl.searchParams.set('filter_date', date);
            } else {
                currentUrl.searchParams.delete('filter_date');
            }

            if (doctor) {
                currentUrl.searchParams.set('filter_doctor', doctor);
            } else {
                currentUrl.searchParams.delete('filter_doctor');
            }

            if (department) {
                currentUrl.searchParams.set('filter_department', department);
            } else {
                currentUrl.searchParams.delete('filter_department');
            }

            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันล้างตัวกรองทั้งหมด
        function clearAllFilters() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('filter_date');
            currentUrl.searchParams.delete('filter_doctor');
            currentUrl.searchParams.delete('filter_department');
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันยืนยันการลบ
        function confirmDelete(id, name) {
            if (confirm('คุณต้องการลบตารางเวร "' + name + '" หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = '<input type="hidden" name="delete_schedule" value="1"><input type="hidden" name="schedule_id" value="' + id + '">';
                document.body.appendChild(form);
                form.submit();
            }
        }

        // เรียกใช้เมื่อโหลดหน้าเสร็จ
        document.addEventListener('DOMContentLoaded', function() {
            addQuickDateButtons();

            // เริ่มต้น Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // เพิ่ม event listeners สำหรับฟอร์มเพิ่มตารางเวร
            if (document.getElementById('startDate')) {
                // Event listeners สำหรับ checkbox form ใหม่
                document.querySelectorAll('input[name="schedule_period"]').forEach(radio => {
                    radio.addEventListener('change', updatePeriodOptions);
                });

                document.getElementById('startDate').addEventListener('change', updateDateRange);

                document.querySelectorAll('.shift-checkbox').forEach(cb => {
                    cb.addEventListener('change', updateSelectionSummary);
                });

                // อัปเดตข้อมูลเริ่มต้น
                updateDateRange();
                updateSelectionSummary();
            } else if (document.getElementById('scheduleDate')) {
                document.querySelectorAll('input[name="date_mode"]').forEach(radio => {
                    radio.addEventListener('change', handleDateModeChange);
                });

                document.getElementById('scheduleDate').addEventListener('change', updateDateInfo);
                document.getElementById('startDate').addEventListener('change', updateMultipleDatesPreview);
                document.getElementById('endDate').addEventListener('change', updateMultipleDatesPreview);
                document.getElementById('patternStartDate').addEventListener('change', updateWeeklyPatternPreview);
                document.getElementById('weeksCount').addEventListener('change', updateWeeklyPatternPreview);

                document.querySelectorAll('input[name="selected_days[]"]').forEach(checkbox => {
                    checkbox.addEventListener('change', updateWeeklyPatternPreview);
                });

                // Event listeners สำหรับรายเดือน
                document.getElementById('targetMonth').addEventListener('change', updateMonthlyPatternPreview);
                document.getElementById('monthlyPatternType').addEventListener('change', handleMonthlyPatternChange);
                document.getElementById('excludeHolidays').addEventListener('change', updateMonthlyPatternPreview);

                document.querySelectorAll('input[name="monthly_selected_days[]"]').forEach(checkbox => {
                    checkbox.addEventListener('change', updateMonthlyPatternPreview);
                });

                // เรียกใช้ครั้งแรก
                handleDateModeChange();
                updateDateInfo();
                handleMonthlyPatternChange();
            }
        });
    </script>
</body>
</html>
