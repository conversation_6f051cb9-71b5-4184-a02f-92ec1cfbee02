<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอิน
requireLogin();

$database = new Database();
$db = $database->getConnection();

// ดึงข้อมูลสถิติ
$stats = [];

// จำนวนแพทย์ทั้งหมด
$query = "SELECT COUNT(*) as total FROM doctors WHERE status = 'active'";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_doctors'] = $stmt->fetch()['total'];

// จำนวนแผนก
$query = "SELECT COUNT(*) as total FROM departments";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_departments'] = $stmt->fetch()['total'];

// จำนวนเวรวันนี้
$query = "SELECT COUNT(*) as total FROM schedules WHERE schedule_date = CURDATE()";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['today_schedules'] = $stmt->fetch()['total'];

// จำนวนเวรสัปดาห์นี้
$query = "SELECT COUNT(*) as total FROM schedules 
          WHERE schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
          AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['week_schedules'] = $stmt->fetch()['total'];

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$current_user = $auth->getCurrentUser();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ด - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    แดชบอร์ด - ระบบตารางเวรแพทย์
                </h1>
                <p class="text-muted">
                    ยินดีต้อนรับ, <?php echo htmlspecialchars($current_user['first_name'] . ' ' . $current_user['last_name']); ?>
                    <?php if ($current_user['department_name']): ?>
                        - แผนก<?php echo htmlspecialchars($current_user['department_name']); ?>
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['total_doctors']); ?></h4>
                                <p class="card-text">แพทย์ทั้งหมด</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-md fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['total_departments']); ?></h4>
                                <p class="card-text">แผนกทั้งหมด</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['today_schedules']); ?></h4>
                                <p class="card-text">เวรวันนี้</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-day fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['week_schedules']); ?></h4>
                                <p class="card-text">เวรสัปดาห์นี้</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-week fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            การดำเนินการด่วน
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="schedule.php?action=add" class="btn btn-primary w-100">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    เพิ่มตารางเวร
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="doctors.php?action=add" class="btn btn-success w-100">
                                    <i class="fas fa-user-plus me-2"></i>
                                    เพิ่มแพทย์ใหม่
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="reports.php?type=weekly" class="btn btn-info w-100">
                                    <i class="fas fa-chart-line me-2"></i>
                                    รายงานสัปดาห์
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="schedule.php?view=calendar" class="btn btn-warning w-100">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    ดูปฏิทิน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>
                            กิจกรรมล่าสุด
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีกิจกรรมล่าสุด</h5>
                            <p class="text-muted">เริ่มใช้งานระบบเพื่อดูกิจกรรมต่างๆ</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            ข้อมูลระบบ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>เวอร์ชัน:</strong> 1.0.0<br>
                            <strong>ผู้ใช้งาน:</strong> <?php echo htmlspecialchars($current_user['username']); ?><br>
                            <strong>สิทธิ์:</strong> <?php echo htmlspecialchars($current_user['role']); ?><br>
                            <strong>เข้าสู่ระบบล่าสุด:</strong> <?php echo $current_user['last_login'] ? date('d/m/Y H:i', strtotime($current_user['last_login'])) : 'ไม่มีข้อมูล'; ?>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="profile.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-user me-1"></i>
                                แก้ไขโปรไฟล์
                            </a>
                            <a href="logout.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                ออกจากระบบ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
