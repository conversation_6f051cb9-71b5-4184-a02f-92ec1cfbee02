<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอินและสิทธิ์
requireLogin();
requireModulePermission('doctors', 'view');

$database = new Database();
$db = $database->getConnection();

$message = '';
$action = $_GET['action'] ?? 'list';
$doctor_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_doctor']) && $auth->hasModulePermission('doctors', 'add')) {
        // ตรวจสอบและแปลงค่าว่างเป็น NULL สำหรับ email
        $email = !empty($_POST['email']) ? $_POST['email'] : null;
        $phone = !empty($_POST['phone']) ? $_POST['phone'] : null;
        $position = !empty($_POST['position']) ? $_POST['position'] : null;
        $specialization = !empty($_POST['specialization']) ? $_POST['specialization'] : null;
        $license_number = !empty($_POST['license_number']) ? $_POST['license_number'] : null;

        $query = "INSERT INTO doctors (doctor_code, fullname, email, phone, department_id, position, specialization, license_number)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);

        try {
            if ($stmt->execute([
                $_POST['doctor_code'],
                $_POST['fullname'],
                $email,
                $phone,
                $_POST['department_id'] ?: null,
                $position,
                $specialization,
                $license_number
            ])) {
                $auth->logActivity('add_doctor', 'doctors', $db->lastInsertId(), null, $_POST);
                $message = showAlert('เพิ่มข้อมูลแพทย์เรียบร้อยแล้ว', 'success');
                $action = 'list';
            }
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                // Duplicate entry error
                if (strpos($e->getMessage(), 'doctor_code') !== false) {
                    $message = showAlert('รหัสแพทย์นี้มีอยู่ในระบบแล้ว', 'warning');
                } elseif (strpos($e->getMessage(), 'email') !== false) {
                    $message = showAlert('อีเมลนี้มีอยู่ในระบบแล้ว', 'warning');
                } else {
                    $message = showAlert('ข้อมูลซ้ำในระบบ', 'warning');
                }
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการเพิ่มข้อมูล: ' . $e->getMessage(), 'error');
            }
        }
    }

    if (isset($_POST['update_doctor']) && $auth->hasModulePermission('doctors', 'edit')) {
        // ดึงข้อมูลเก่าสำหรับ log
        $old_data_query = "SELECT * FROM doctors WHERE id = ?";
        $old_stmt = $db->prepare($old_data_query);
        $old_stmt->execute([$doctor_id]);
        $old_data = $old_stmt->fetch();

        // ตรวจสอบและแปลงค่าว่างเป็น NULL สำหรับ email
        $email = !empty($_POST['email']) ? $_POST['email'] : null;
        $phone = !empty($_POST['phone']) ? $_POST['phone'] : null;
        $position = !empty($_POST['position']) ? $_POST['position'] : null;
        $specialization = !empty($_POST['specialization']) ? $_POST['specialization'] : null;
        $license_number = !empty($_POST['license_number']) ? $_POST['license_number'] : null;

        $query = "UPDATE doctors SET fullname=?, email=?, phone=?, department_id=?, position=?, specialization=?, license_number=?, status=? WHERE id=?";
        $stmt = $db->prepare($query);

        try {
            if ($stmt->execute([
                $_POST['fullname'],
                $email,
                $phone,
                $_POST['department_id'] ?: null,
                $position,
                $specialization,
                $license_number,
                $_POST['status'],
                $doctor_id
            ])) {
                $auth->logActivity('update_doctor', 'doctors', $doctor_id, $old_data, $_POST);
                $message = showAlert('อัปเดตข้อมูลแพทย์เรียบร้อยแล้ว', 'success');
                $action = 'list';
            }
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                // Duplicate entry error
                if (strpos($e->getMessage(), 'email') !== false) {
                    $message = showAlert('อีเมลนี้มีอยู่ในระบบแล้ว', 'warning');
                } else {
                    $message = showAlert('ข้อมูลซ้ำในระบบ', 'warning');
                }
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตข้อมูล: ' . $e->getMessage(), 'error');
            }
        }
    }
    
    if (isset($_POST['delete_doctor']) && $auth->hasModulePermission('doctors', 'delete')) {
        $delete_doctor_id = $_POST['doctor_id'] ?? null;

        if ($delete_doctor_id) {
            // ดึงข้อมูลเก่าสำหรับ log
            $old_data_query = "SELECT * FROM doctors WHERE id = ?";
            $old_stmt = $db->prepare($old_data_query);
            $old_stmt->execute([$delete_doctor_id]);
            $old_data = $old_stmt->fetch();

            // ตรวจสอบว่ามีตารางเวรหรือไม่
            $check_query = "SELECT COUNT(*) as count FROM schedules WHERE doctor_id = ?";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->execute([$delete_doctor_id]);
            $schedule_count = $check_stmt->fetch()['count'];

            if ($schedule_count > 0) {
                $message = showAlert('ไม่สามารถลบแพทย์ได้ เนื่องจากมีตารางเวรที่เกี่ยวข้อง (' . $schedule_count . ' ตารางเวร)', 'warning');
            } else {
                $query = "DELETE FROM doctors WHERE id = ?";
                $stmt = $db->prepare($query);

                if ($stmt->execute([$delete_doctor_id])) {
                    $auth->logActivity('delete_doctor', 'doctors', $delete_doctor_id, $old_data);
                    $message = showAlert('ลบข้อมูลแพทย์เรียบร้อยแล้ว', 'success');
                } else {
                    $message = showAlert('เกิดข้อผิดพลาดในการลบข้อมูล', 'error');
                }
            }
        } else {
            $message = showAlert('ไม่พบข้อมูลแพทย์ที่ต้องการลบ', 'error');
        }
        $action = 'list';
    }
}

// Get departments for dropdown
$dept_query = "SELECT * FROM departments ORDER BY name";
$dept_stmt = $db->prepare($dept_query);
$dept_stmt->execute();
$departments = $dept_stmt->fetchAll();

// Get doctor data for edit
$doctor = null;
if ($action == 'edit' && $doctor_id) {
    $query = "SELECT * FROM doctors WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$doctor_id]);
    $doctor = $stmt->fetch();
}

// Get all doctors for list
if ($action == 'list') {
    $search = $_GET['search'] ?? '';
    $department_filter = $_GET['department'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $query = "SELECT d.*, dept.name as department_name 
              FROM doctors d 
              LEFT JOIN departments dept ON d.department_id = dept.id 
              WHERE 1=1";
    $params = [];
    
    if ($search) {
        $query .= " AND (d.doctor_code LIKE ? OR d.fullname LIKE ? OR d.email LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param]);
    }
    
    if ($department_filter) {
        $query .= " AND d.department_id = ?";
        $params[] = $department_filter;
    }
    
    if ($status_filter) {
        $query .= " AND d.status = ?";
        $params[] = $status_filter;
    }
    
    $query .= " ORDER BY d.doctor_code";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $doctors = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการแพทย์ - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-user-md me-2"></i>
                            จัดการแพทย์
                        </h1>
                        <p class="text-muted">จัดการข้อมูลแพทย์ในระบบ</p>
                    </div>
                    <?php if ($action == 'list' && $auth->hasModulePermission('doctors', 'add')): ?>
                        <a href="?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>เพิ่มแพทย์ใหม่
                        </a>
                    <?php elseif ($action != 'list'): ?>
                        <a href="doctors.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>กลับ
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($action == 'list'): ?>
            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">ค้นหา</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" placeholder="รหัสแพทย์, ชื่อ, อีเมล">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">แผนก</label>
                            <select class="form-select" name="department">
                                <option value="">ทุกแผนก</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>" <?php echo ($_GET['department'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo $dept['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">สถานะ</label>
                            <select class="form-select" name="status">
                                <option value="">ทุกสถานะ</option>
                                <option value="active" <?php echo ($_GET['status'] ?? '') == 'active' ? 'selected' : ''; ?>>ใช้งาน</option>
                                <option value="inactive" <?php echo ($_GET['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>ไม่ใช้งาน</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>ค้นหา
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Doctors List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        รายชื่อแพทย์ (<?php echo count($doctors); ?> คน)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($doctors)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่พบข้อมูลแพทย์</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>รหัสแพทย์</th>
                                        <th>ชื่อ-นามสกุล</th>
                                        <th>แผนก</th>
                                        <th>ตำแหน่ง</th>
                                        <th>ความเชี่ยวชาญ</th>
                                        <th>อีเมล</th>
                                        <th>สถานะ</th>
                                        <th>จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($doctors as $doc): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $doc['doctor_code']; ?></strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="doctor-avatar me-3">
                                                        <?php echo strtoupper(substr($doc['fullname'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo $doc['fullname']; ?></strong>
                                                        <?php if ($doc['phone']): ?>
                                                            <br><small class="text-muted"><?php echo $doc['phone']; ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo $doc['department_name'] ?? '-'; ?></td>
                                            <td><?php echo $doc['position'] ?? '-'; ?></td>
                                            <td><?php echo $doc['specialization'] ?? '-'; ?></td>
                                            <td><?php echo $doc['email'] ?? '-'; ?></td>
                                            <td>
                                                <?php if ($doc['status'] == 'active'): ?>
                                                    <span class="badge bg-success">
                                                        <span class="status-indicator status-active"></span>
                                                        ใช้งาน
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <span class="status-indicator status-inactive"></span>
                                                        ไม่ใช้งาน
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($auth->hasModulePermission('doctors', 'edit')): ?>
                                                        <a href="?action=edit&id=<?php echo $doc['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($auth->hasModulePermission('doctors', 'delete')): ?>
                                                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete(<?php echo $doc['id']; ?>, '<?php echo htmlspecialchars($doc['fullname']); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action == 'add' || $action == 'edit'): ?>
            <!-- Add/Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-<?php echo $action == 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action == 'add' ? 'เพิ่มแพทย์ใหม่' : 'แก้ไขข้อมูลแพทย์'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <?php if ($action == 'add'): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">รหัสแพทย์ <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="doctor_code" required placeholder="เช่น DOC001">
                                </div>
                            <?php endif; ?>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="fullname" value="<?php echo htmlspecialchars($doctor['fullname'] ?? ''); ?>" required placeholder="เช่น นพ.สมชาย ใจดี">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">อีเมล</label>
                                <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($doctor['email'] ?? ''); ?>" placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">เบอร์โทรศัพท์</label>
                                <input type="text" class="form-control" name="phone" value="<?php echo htmlspecialchars($doctor['phone'] ?? ''); ?>" placeholder="************">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">แผนก</label>
                                <select class="form-select" name="department_id">
                                    <option value="">เลือกแผนก</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" <?php echo ($doctor['department_id'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($dept['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ตำแหน่ง</label>
                                <input type="text" class="form-control" name="position" value="<?php echo htmlspecialchars($doctor['position'] ?? ''); ?>" placeholder="แพทย์ประจำ">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ความเชี่ยวชาญ</label>
                                <input type="text" class="form-control" name="specialization" value="<?php echo htmlspecialchars($doctor['specialization'] ?? ''); ?>" placeholder="อายุรกรรม">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">เลขใบประกอบวิชาชีพ</label>
                                <input type="text" class="form-control" name="license_number" value="<?php echo htmlspecialchars($doctor['license_number'] ?? ''); ?>" placeholder="MD12345">
                            </div>
                            <?php if ($action == 'edit'): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">สถานะ</label>
                                    <select class="form-select" name="status">
                                        <option value="active" <?php echo ($doctor['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>ใช้งาน</option>
                                        <option value="inactive" <?php echo ($doctor['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>ไม่ใช้งาน</option>
                                    </select>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="doctors.php" class="btn btn-secondary">ยกเลิก</a>
                            <button type="submit" name="<?php echo $action == 'add' ? 'add_doctor' : 'update_doctor'; ?>" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?php echo $action == 'add' ? 'เพิ่มข้อมูล' : 'บันทึกการแก้ไข'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณต้องการลบข้อมูลแพทย์ <strong id="doctorName"></strong> หรือไม่?</p>
                    <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถยกเลิกได้</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <form method="POST" style="display: inline;" id="deleteForm">
                        <input type="hidden" id="deleteId" name="doctor_id">
                        <button type="submit" name="delete_doctor" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>ลบข้อมูล
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteId').value = id;
            document.getElementById('doctorName').textContent = name;

            // แสดง modal
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // ตรวจสอบก่อนส่งฟอร์มลบ
        document.getElementById('deleteForm').addEventListener('submit', function(e) {
            const doctorName = document.getElementById('doctorName').textContent;
            if (!confirm('คุณแน่ใจหรือไม่ที่จะลบแพทย์ ' + doctorName + '?')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>
