<?php
require_once 'auth.php';

// ตรวจสอบการล็อกอิน
requireLogin();

$message = '';

// ประมวลผลการเปลี่ยนรหัสผ่าน
if ($_POST && isset($_POST['change_password'])) {
    $old_password = $_POST['old_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($old_password) || empty($new_password) || empty($confirm_password)) {
        $message = showAlert('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
    } elseif ($new_password !== $confirm_password) {
        $message = showAlert('รหัสผ่านใหม่และการยืนยันไม่ตรงกัน', 'error');
    } else {
        // ตรวจสอบความแข็งแกร่งของรหัสผ่าน
        $password_errors = $auth->validatePasswordStrength($new_password);
        if (!empty($password_errors)) {
            $message = showAlert('รหัสผ่านไม่ปลอดภัย: ' . implode(', ', $password_errors), 'error');
        } else {
            if ($auth->changePassword($old_password, $new_password)) {
                $message = showAlert('เปลี่ยนรหัสผ่านเรียบร้อยแล้ว', 'success');
            } else {
                $message = showAlert('รหัสผ่านเก่าไม่ถูกต้อง', 'error');
            }
        }
    }
}

$current_user = $auth->getCurrentUser();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เปลี่ยนรหัสผ่าน - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-key me-2"></i>
                            เปลี่ยนรหัสผ่าน
                        </h1>
                        <p class="text-muted">เปลี่ยนรหัสผ่านสำหรับบัญชี <?php echo htmlspecialchars($current_user['username']); ?></p>
                    </div>
                    <a href="profile.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>กลับ
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lock me-2"></i>
                            เปลี่ยนรหัสผ่าน
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="changePasswordForm">
                            <div class="mb-3">
                                <label class="form-label">รหัสผ่านเก่า <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="old_password" id="oldPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('oldPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">รหัสผ่านใหม่ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="new_password" id="newPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('newPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร ประกอบด้วย:
                                    <ul class="mt-2 mb-0">
                                        <li>ตัวอักษรพิมพ์ใหญ่ (A-Z)</li>
                                        <li>ตัวอักษรพิมพ์เล็ก (a-z)</li>
                                        <li>ตัวเลข (0-9)</li>
                                    </ul>
                                </div>
                                
                                <!-- Password Strength Indicator -->
                                <div class="mt-2">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small id="passwordStrengthText" class="text-muted"></small>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label">ยืนยันรหัสผ่านใหม่ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="confirm_password" id="confirmPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirmPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div id="passwordMatch" class="form-text"></div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" name="change_password" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>เปลี่ยนรหัสผ่าน
                                </button>
                                <a href="profile.php" class="btn btn-outline-secondary">ยกเลิก</a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Security Tips -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            เคล็ดลับความปลอดภัย
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>ใช้รหัสผ่านที่แข็งแกร่งและไม่ซ้ำกับบัญชีอื่น</li>
                            <li>เปลี่ยนรหัสผ่านเป็นประจำทุก 3-6 เดือน</li>
                            <li>ไม่แชร์รหัสผ่านกับผู้อื่น</li>
                            <li>ออกจากระบบเมื่อใช้งานเสร็จ</li>
                            <li>ใช้อุปกรณ์ที่เชื่อถือได้ในการเข้าถึงระบบ</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword(inputId, button) {
            const input = document.getElementById(inputId);
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Password strength checker
        document.getElementById('newPassword').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            const strengthText = document.getElementById('passwordStrengthText');
            
            let strength = 0;
            let feedback = [];
            
            // Length check
            if (password.length >= 8) strength += 25;
            else feedback.push('อย่างน้อย 8 ตัวอักษร');
            
            // Uppercase check
            if (/[A-Z]/.test(password)) strength += 25;
            else feedback.push('ตัวพิมพ์ใหญ่');
            
            // Lowercase check
            if (/[a-z]/.test(password)) strength += 25;
            else feedback.push('ตัวพิมพ์เล็ก');
            
            // Number check
            if (/[0-9]/.test(password)) strength += 25;
            else feedback.push('ตัวเลข');
            
            // Update progress bar
            strengthBar.style.width = strength + '%';
            
            if (strength < 50) {
                strengthBar.className = 'progress-bar bg-danger';
                strengthText.textContent = 'อ่อน - ต้องการ: ' + feedback.join(', ');
                strengthText.className = 'text-danger';
            } else if (strength < 75) {
                strengthBar.className = 'progress-bar bg-warning';
                strengthText.textContent = 'ปานกลาง - ต้องการ: ' + feedback.join(', ');
                strengthText.className = 'text-warning';
            } else if (strength < 100) {
                strengthBar.className = 'progress-bar bg-info';
                strengthText.textContent = 'ดี - ต้องการ: ' + feedback.join(', ');
                strengthText.className = 'text-info';
            } else {
                strengthBar.className = 'progress-bar bg-success';
                strengthText.textContent = 'แข็งแกร่ง';
                strengthText.className = 'text-success';
            }
        });

        // Password match checker
        function checkPasswordMatch() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchDiv = document.getElementById('passwordMatch');
            
            if (confirmPassword === '') {
                matchDiv.textContent = '';
                return;
            }
            
            if (newPassword === confirmPassword) {
                matchDiv.textContent = '✓ รหัสผ่านตรงกัน';
                matchDiv.className = 'form-text text-success';
            } else {
                matchDiv.textContent = '✗ รหัสผ่านไม่ตรงกัน';
                matchDiv.className = 'form-text text-danger';
            }
        }

        document.getElementById('newPassword').addEventListener('input', checkPasswordMatch);
        document.getElementById('confirmPassword').addEventListener('input', checkPasswordMatch);

        // Form validation
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('รหัสผ่านใหม่และการยืนยันไม่ตรงกัน');
                return false;
            }
            
            // Check password strength
            let strength = 0;
            if (newPassword.length >= 8) strength += 25;
            if (/[A-Z]/.test(newPassword)) strength += 25;
            if (/[a-z]/.test(newPassword)) strength += 25;
            if (/[0-9]/.test(newPassword)) strength += 25;
            
            if (strength < 100) {
                if (!confirm('รหัสผ่านของคุณยังไม่แข็งแกร่งพอ คุณต้องการดำเนินการต่อหรือไม่?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    </script>
</body>
</html>
