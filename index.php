<?php
require_once 'config/database.php';
require_once 'auth.php';

// หน้า index.php เป็นหน้า public view ไม่ต้อง login
// แต่ถ้า login แล้วจะแสดง navbar แบบเต็ม

$database = new Database();
$db = $database->getConnection();

// ดึงข้อมูลสถิติ
$stats = [];

// จำนวนแพทย์ (กรองตามแผนกถ้ามีการเลือก)
if ($selected_department) {
    $query = "SELECT COUNT(*) as total FROM doctors WHERE status = 'active' AND department_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_department]);
} else {
    $query = "SELECT COUNT(*) as total FROM doctors WHERE status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute();
}
$stats['total_doctors'] = $stmt->fetch()['total'];

// จำนวนแผนก (แสดงทั้งหมดเสมอ)
$query = "SELECT COUNT(*) as total FROM departments";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_departments'] = $stmt->fetch()['total'];

// ดึงข้อมูลแผนกทั้งหมด
$query = "SELECT * FROM departments ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$departments = $stmt->fetchAll();

// รับค่าแผนกที่เลือก
$selected_department = $_GET['department'] ?? null;

// จำนวนเวรวันนี้ (กรองตามแผนกถ้ามีการเลือก)
$query = "SELECT COUNT(*) as total FROM schedules s";
if ($selected_department) {
    $query .= " JOIN doctors d ON s.doctor_id = d.id WHERE s.schedule_date = CURDATE() AND d.department_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_department]);
} else {
    $query .= " WHERE schedule_date = CURDATE()";
    $stmt = $db->prepare($query);
    $stmt->execute();
}
$stats['today_schedules'] = $stmt->fetch()['total'];

// จำนวนเวรสัปดาห์นี้ (กรองตามแผนกถ้ามีการเลือก)
$query = "SELECT COUNT(*) as total FROM schedules s";
if ($selected_department) {
    $query .= " JOIN doctors d ON s.doctor_id = d.id
                WHERE s.schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)
                AND d.department_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_department]);
} else {
    $query .= " WHERE schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)";
    $stmt = $db->prepare($query);
    $stmt->execute();
}
$stats['week_schedules'] = $stmt->fetch()['total'];

// ดึงตารางเวรวันนี้ (กรองตามแผนกถ้ามีการเลือก)
$query = "SELECT s.*, d.fullname, d.doctor_code,
                 st.name as shift_name, st.start_time, st.end_time, st.color,
                 dept.name as department_name
          FROM schedules s
          JOIN doctors d ON s.doctor_id = d.id
          JOIN shift_types st ON s.shift_type_id = st.id
          LEFT JOIN departments dept ON d.department_id = dept.id
          WHERE s.schedule_date = CURDATE()";

$params = [];
if ($selected_department) {
    $query .= " AND d.department_id = ?";
    $params[] = $selected_department;
}

$query .= " ORDER BY st.start_time";
$stmt = $db->prepare($query);
$stmt->execute($params);
$today_schedules = $stmt->fetchAll();

// ดึงตารางเวรสัปดาห์นี้
$query = "SELECT s.*, d.first_name, d.last_name, d.doctor_code,
                 st.name as shift_name, st.start_time, st.end_time, st.color,
                 dept.name as department_name
          FROM schedules s
          JOIN doctors d ON s.doctor_id = d.id
          JOIN shift_types st ON s.shift_type_id = st.id
          LEFT JOIN departments dept ON d.department_id = dept.id
          WHERE s.schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) 
          AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)
          ORDER BY s.schedule_date, st.start_time";
$stmt = $db->prepare($query);
$stmt->execute();
$week_schedules = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php if ($auth->isLoggedIn()): ?>
        <!-- แสดง navbar แบบเต็มเมื่อ login แล้ว -->
        <?php include 'includes/navbar.php'; ?>
    <?php else: ?>
        <!-- แสดง navbar แบบ public เมื่อยังไม่ login -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <i class="fas fa-hospital me-2"></i>
                    ระบบตารางเวรแพทย์
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="fas fa-home me-1"></i>หน้าแรก
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php endif; ?>

    <div class="container mt-4">
        <!-- Success Message -->
        <?php if (isset($_GET['message']) && $_GET['message'] == 'logout_success'): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            ออกจากระบบเรียบร้อยแล้ว
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-calendar-alt me-2"></i>
                    ระบบตารางเวรแพทย์
                </h1>
                <?php if ($auth->isLoggedIn()): ?>
                    <?php $current_user = $auth->getCurrentUser(); ?>
                    <p class="text-muted">
                        ยินดีต้อนรับ, <?php echo htmlspecialchars($current_user['first_name'] . ' ' . $current_user['last_name']); ?>
                        <?php if ($current_user['department_name']): ?>
                            - แผนก<?php echo htmlspecialchars($current_user['department_name']); ?>
                        <?php endif; ?>
                    </p>
                <?php else: ?>
                    <p class="text-muted">ตรวจสอบตารางเวรแพทย์ประจำวัน</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Filter Indicator -->
        <?php if ($selected_department): ?>
            <?php
            $dept_name = '';
            foreach ($departments as $dept) {
                if ($dept['id'] == $selected_department) {
                    $dept_name = $dept['name'];
                    break;
                }
            }
            ?>
            <div class="row mb-3">
                <div class="col-12">
                    <div class="filter-indicator d-flex justify-content-between align-items-center py-2 px-3">
                        <div>
                            <i class="fas fa-filter me-2 text-primary"></i>
                            <strong>กำลังแสดงข้อมูลของแผนก<?php echo $dept_name; ?></strong>
                            <small class="text-muted ms-2">- ข้อมูลสถิติและตารางเวรจะแสดงเฉพาะแผนกนี้</small>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearDepartmentFilter()">
                            <i class="fas fa-times me-1"></i>แสดงทุกแผนก
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4 <?php echo $selected_department ? 'stats-filtered' : ''; ?>">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['total_doctors']); ?></h4>
                                <p class="card-text">
                                    <?php if ($selected_department): ?>
                                        แพทย์ในแผนก
                                    <?php else: ?>
                                        แพทย์ทั้งหมด
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-md fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['total_departments']); ?></h4>
                                <p class="card-text">แผนกทั้งหมด</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['today_schedules']); ?></h4>
                                <p class="card-text">
                                    <?php if ($selected_department): ?>
                                        เวรวันนี้ (แผนก)
                                    <?php else: ?>
                                        เวรวันนี้
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-day fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['week_schedules']); ?></h4>
                                <p class="card-text">
                                    <?php if ($selected_department): ?>
                                        เวรสัปดาห์นี้ (แผนก)
                                    <?php else: ?>
                                        เวรสัปดาห์นี้
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-week fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Schedule -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">
                                    <i class="fas fa-calendar-day me-1"></i>
                                    ตารางเวรวันนี้
                                </h6>
                                <small class="text-muted">
                                    <?php echo getThaiDayName(date('Y-m-d')) . ' ' . formatThaiDate(date('Y-m-d')); ?>
                                    <?php if ($selected_department): ?>
                                        <?php
                                        $dept_name = '';
                                        foreach ($departments as $dept) {
                                            if ($dept['id'] == $selected_department) {
                                                $dept_name = $dept['name'];
                                                break;
                                            }
                                        }
                                        ?>
                                        - แผนก<?php echo $dept_name; ?>
                                    <?php endif; ?>
                                </small>
                            </div>

                            <!-- Department Filter -->
                            <div class="d-flex gap-2 align-items-center">
                                <small class="text-muted">แผนก:</small>
                                <select id="departmentFilter" class="form-select form-select-sm" onchange="filterByDepartment()" style="width: 180px;">
                                    <option value="">ทุกแผนก</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>"
                                                <?php echo $selected_department == $dept['id'] ? 'selected' : ''; ?>>
                                            <?php echo $dept['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>

                                <?php if ($selected_department): ?>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDepartmentFilter()" title="แสดงทุกแผนก">
                                        <i class="fas fa-times"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!$selected_department): ?>
                            <!-- แสดงเมื่อยังไม่ได้เลือกแผนก -->
                            <div class="text-center py-4">
                                <i class="fas fa-building fa-2x text-primary mb-2"></i>
                                <h6 class="text-muted mb-2">เลือกแผนกเพื่อดูตารางเวรวันนี้</h6>
                                <p class="text-muted small mb-3">กรุณาเลือกแผนกจากเมนูด้านบนเพื่อดูตารางเวรของแพทย์ในแผนกนั้น</p>

                                <!-- แสดงรายการแผนกให้เลือก -->
                                <div class="row justify-content-center">
                                    <?php foreach ($departments as $dept): ?>
                                        <div class="col-md-4 col-lg-3 mb-2">
                                            <div class="card department-card h-100" onclick="selectDepartment(<?php echo $dept['id']; ?>)" style="cursor: pointer;">
                                                <div class="card-body text-center py-3">
                                                    <i class="fas fa-building fa-lg text-primary mb-1"></i>
                                                    <h6 class="card-title mb-1"><?php echo $dept['name']; ?></h6>
                                                    <small class="text-muted">คลิกเพื่อดูตารางเวร</small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php elseif (empty($today_schedules)): ?>
                            <!-- แสดงเมื่อเลือกแผนกแล้วแต่ไม่มีตารางเวร -->
                            <div class="text-center py-3">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                                <?php
                                $dept_name = '';
                                foreach ($departments as $dept) {
                                    if ($dept['id'] == $selected_department) {
                                        $dept_name = $dept['name'];
                                        break;
                                    }
                                }
                                ?>
                                <h6 class="text-muted">ไม่มีตารางเวรสำหรับแผนก<?php echo $dept_name; ?></h6>
                                <p class="text-muted small">ยังไม่มีการจัดตารางเวรสำหรับแผนก<?php echo $dept_name; ?>ในวันนี้</p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDepartmentFilter()">
                                        <i class="fas fa-eye me-1"></i>เลือกแผนกอื่น
                                    </button>
                                    <?php if (!$auth->isLoggedIn()): ?>
                                        <a href="login.php" class="btn btn-primary btn-sm">
                                            <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบเพื่อจัดการ
                                        </a>
                                    <?php else: ?>
                                        <a href="schedule.php?action=add" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>เพิ่มตารางเวร
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- แสดงตารางเวรแบบจัดกลุ่มตามเวลา -->
                            <div class="schedule-groups index-schedule-container">
                                <?php
                                // จัดกลุ่มตารางเวรตามประเภทเวรและเวลา
                                $shifts_by_time = [];
                                foreach ($today_schedules as $schedule) {
                                    $shift_key = $schedule['shift_type_id'] . '_' . $schedule['start_time'] . '_' . $schedule['end_time'];
                                    if (!isset($shifts_by_time[$shift_key])) {
                                        $shifts_by_time[$shift_key] = [
                                            'shift_info' => $schedule,
                                            'doctors' => []
                                        ];
                                    }
                                    $shifts_by_time[$shift_key]['doctors'][] = $schedule;
                                }

                                // เรียงลำดับตามเวลาเริ่มต้น
                                uasort($shifts_by_time, function($a, $b) {
                                    return strcmp($a['shift_info']['start_time'], $b['shift_info']['start_time']);
                                });
                                ?>

                                <?php foreach ($shifts_by_time as $shift_group):
                                    $shift_info = $shift_group['shift_info'];
                                    $doctors = $shift_group['doctors'];
                                ?>
                                    <div class="shift-card">
                                        <div class="shift-header">
                                            <div class="shift-time">
                                                <?php echo formatTime($shift_info['start_time']) . ' - ' . formatTime($shift_info['end_time']); ?>
                                            </div>
                                            <div class="shift-name">
                                                <?php echo $shift_info['shift_name']; ?>
                                            </div>
                                            <div class="shift-count">
                                                <?php echo count($doctors); ?> คน
                                            </div>
                                        </div>

                                        <div class="doctors-list">
                                            <?php foreach ($doctors as $doctor): ?>
                                                <div class="doctor-item">
                                                    <div class="doctor-name">
                                                        <?php echo $doctor['fullname']; ?>
                                                    </div>
                                                    <?php if ($doctor['notes']): ?>
                                                        <div class="doctor-note">
                                                            <?php echo htmlspecialchars($doctor['notes']); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Public Information (แสดงเฉพาะเมื่อยังไม่ login) -->
        <?php if (!$auth->isLoggedIn()): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            ข้อมูลสำหรับผู้ป่วยและญาติ
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <div class="alert alert-info py-2">
                                    <h6 class="mb-1"><i class="fas fa-clock me-1"></i>เวลาทำการ</h6>
                                    <p class="mb-0 small">
                                        <strong>เวรเช้า:</strong> 08:00 - 16:00<br>
                                        <strong>เวรบ่าย:</strong> 16:00 - 24:00<br>
                                        <strong>เวรดึก:</strong> 00:00 - 08:00
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="alert alert-warning py-2">
                                    <h6 class="mb-1"><i class="fas fa-exclamation-triangle me-1"></i>หมายเหตุ</h6>
                                    <p class="mb-0 small">
                                        ตารางเวรอาจมีการเปลี่ยนแปลงตามความเหมาะสม<br>
                                        กรุณาติดต่อแผนกที่เกี่ยวข้องเพื่อยืนยันข้อมูล<br>
                                        <strong>โทร:</strong> 02-XXX-XXXX
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="login.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                เข้าสู่ระบบสำหรับเจ้าหน้าที่
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- Quick Actions สำหรับผู้ใช้ที่ login แล้ว -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-bolt me-1"></i>
                            การดำเนินการด่วน
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="schedule.php?action=add" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-plus-circle me-1"></i>
                                    เพิ่มตารางเวร
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="doctors.php?action=add" class="btn btn-success btn-sm w-100">
                                    <i class="fas fa-user-plus me-1"></i>
                                    เพิ่มแพทย์ใหม่
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="reports.php?type=weekly" class="btn btn-info btn-sm w-100">
                                    <i class="fas fa-chart-line me-1"></i>
                                    รายงานสัปดาห์
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="schedule.php?view=calendar" class="btn btn-warning btn-sm w-100">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    ดูปฏิทิน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/script.js"></script>

    <script>
        // ฟังก์ชันกรองตามแผนก
        function filterByDepartment() {
            const departmentId = document.getElementById('departmentFilter').value;
            const currentUrl = new URL(window.location);

            if (departmentId) {
                currentUrl.searchParams.set('department', departmentId);
            } else {
                currentUrl.searchParams.delete('department');
            }

            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันล้างตัวกรองแผนก
        function clearDepartmentFilter() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('department');
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันเลือกแผนก
        function selectDepartment(departmentId) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('department', departmentId);
            window.location.href = currentUrl.toString();
        }

        // เพิ่ม animation เมื่อเปลี่ยนแผนก
        document.addEventListener('DOMContentLoaded', function() {
            // เริ่มต้น Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            const departmentFilter = document.getElementById('departmentFilter');
            if (departmentFilter) {
                departmentFilter.addEventListener('change', function() {
                    // เพิ่ม loading effect
                    const statsCards = document.querySelector('.row.mb-4');
                    const scheduleCard = document.querySelector('.card');

                    if (statsCards) {
                        statsCards.style.opacity = '0.6';
                        statsCards.style.transition = 'opacity 0.3s ease';
                    }

                    if (scheduleCard) {
                        scheduleCard.style.opacity = '0.6';
                        scheduleCard.style.transition = 'opacity 0.3s ease';
                    }

                    // แสดง loading indicator
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'text-center py-3';
                    loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังโหลดข้อมูล...';
                    loadingDiv.id = 'loading-indicator';

                    const container = document.querySelector('.container');
                    if (container && !document.getElementById('loading-indicator')) {
                        container.appendChild(loadingDiv);
                    }
                });
            }

            // เพิ่ม hover effect สำหรับ department cards
            const departmentCards = document.querySelectorAll('.department-card');
            departmentCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                });
            });
        });
    </script>
</body>
</html>
