<?php
require_once 'config/database.php';
require_once 'auth.php';

// หน้า index.php เป็นหน้า public view ไม่ต้อง login
// แต่ถ้า login แล้วจะแสดง navbar แบบเต็ม

$database = new Database();
$db = $database->getConnection();

// รับค่าแผนกและวันที่เลือก
$selected_department = $_GET['department'] ?? null;
$selected_date = $_GET['date'] ?? date('Y-m-d');

// ดึงข้อมูลสถิติ
$stats = [];

// จำนวนแพทย์ (กรองตามแผนกถ้ามีการเลือก)
if ($selected_department) {
    $query = "SELECT COUNT(*) as total FROM doctors WHERE status = 'active' AND department_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_department]);
} else {
    $query = "SELECT COUNT(*) as total FROM doctors WHERE status = 'active'";
    $stmt = $db->prepare($query);
    $stmt->execute();
}
$stats['total_doctors'] = $stmt->fetch()['total'];

// จำนวนแผนก (แสดงทั้งหมดเสมอ)
$query = "SELECT COUNT(*) as total FROM departments";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_departments'] = $stmt->fetch()['total'];

// ดึงข้อมูลแผนกทั้งหมด
$query = "SELECT * FROM departments ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$departments = $stmt->fetchAll();

// จำนวนเวรในวันที่เลือก (กรองตามแผนกถ้ามีการเลือก)
$query = "SELECT COUNT(*) as total FROM schedules s";
if ($selected_department) {
    $query .= " JOIN doctors d ON s.doctor_id = d.id WHERE s.schedule_date = ? AND d.department_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_date, $selected_department]);
} else {
    $query .= " WHERE schedule_date = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_date]);
}
$stats['today_schedules'] = $stmt->fetch()['total'];

// จำนวนเวรสัปดาห์นี้ (กรองตามแผนกถ้ามีการเลือก)
$query = "SELECT COUNT(*) as total FROM schedules s";
if ($selected_department) {
    $query .= " JOIN doctors d ON s.doctor_id = d.id
                WHERE s.schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)
                AND d.department_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$selected_department]);
} else {
    $query .= " WHERE schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)";
    $stmt = $db->prepare($query);
    $stmt->execute();
}
$stats['week_schedules'] = $stmt->fetch()['total'];

// ดึงตารางเวรในวันที่เลือก (กรองตามแผนกถ้ามีการเลือก)
$query = "SELECT s.*, d.fullname, d.doctor_code,
                 st.name as shift_name, st.start_time, st.end_time, st.color,
                 dept.name as department_name
          FROM schedules s
          JOIN doctors d ON s.doctor_id = d.id
          JOIN shift_types st ON s.shift_type_id = st.id
          LEFT JOIN departments dept ON d.department_id = dept.id
          WHERE s.schedule_date = ?";

$params = [$selected_date];
if ($selected_department) {
    $query .= " AND d.department_id = ?";
    $params[] = $selected_department;
}

$query .= " ORDER BY st.start_time";
$stmt = $db->prepare($query);
$stmt->execute($params);
$today_schedules = $stmt->fetchAll();

// ดึงตารางเวรสัปดาห์นี้
$query = "SELECT s.*, d.first_name, d.last_name, d.doctor_code,
                 st.name as shift_name, st.start_time, st.end_time, st.color,
                 dept.name as department_name
          FROM schedules s
          JOIN doctors d ON s.doctor_id = d.id
          JOIN shift_types st ON s.shift_type_id = st.id
          LEFT JOIN departments dept ON d.department_id = dept.id
          WHERE s.schedule_date BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) 
          AND DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)
          ORDER BY s.schedule_date, st.start_time";
$stmt = $db->prepare($query);
$stmt->execute();
$week_schedules = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php if ($auth->isLoggedIn()): ?>
        <!-- แสดง navbar แบบเต็มเมื่อ login แล้ว -->
        <?php include 'includes/navbar.php'; ?>
    <?php else: ?>
        <!-- แสดง navbar แบบ public เมื่อยังไม่ login -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <i class="fas fa-hospital me-2"></i>
                    ระบบตารางเวรแพทย์
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="fas fa-home me-1"></i>หน้าแรก
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php endif; ?>

    <div class="container mt-4">
        <!-- Success Message -->
        <?php if (isset($_GET['message']) && $_GET['message'] == 'logout_success'): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            ออกจากระบบเรียบร้อยแล้ว
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-calendar-alt me-2"></i>
                    ระบบตารางเวรแพทย์
                </h1>
                <?php if ($auth->isLoggedIn()): ?>
                    <?php $current_user = $auth->getCurrentUser(); ?>
                    <p class="text-muted">
                        ยินดีต้อนรับ, <?php echo htmlspecialchars($current_user['first_name'] . ' ' . $current_user['last_name']); ?>
                        <?php if ($current_user['department_name']): ?>
                            - แผนก<?php echo htmlspecialchars($current_user['department_name']); ?>
                        <?php endif; ?>
                    </p>
                <?php else: ?>
                    <p class="text-muted">ตรวจสอบตารางเวรแพทย์ประจำวัน</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Filter Indicator -->
        <?php if ($selected_department): ?>
            <?php
            $dept_name = '';
            foreach ($departments as $dept) {
                if ($dept['id'] == $selected_department) {
                    $dept_name = $dept['name'];
                    break;
                }
            }
            ?>
            <div class="row mb-3">
                <div class="col-12">
                    <div class="filter-indicator d-flex justify-content-between align-items-center py-2 px-3">
                        <div>
                            <i class="fas fa-filter me-2 text-primary"></i>
                            <strong>กำลังแสดงข้อมูลของแผนก<?php echo $dept_name; ?></strong>
                            <small class="text-muted ms-2">- ข้อมูลสถิติและตารางเวรจะแสดงเฉพาะแผนกนี้</small>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearDepartmentFilter()">
                            <i class="fas fa-times me-1"></i>แสดงทุกแผนก
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4 <?php echo $selected_department ? 'stats-filtered' : ''; ?>">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['total_doctors']); ?></h4>
                                <p class="card-text">
                                    <?php if ($selected_department): ?>
                                        แพทย์ในแผนก
                                    <?php else: ?>
                                        แพทย์ทั้งหมด
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-md fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['total_departments']); ?></h4>
                                <p class="card-text">แผนกทั้งหมด</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['today_schedules']); ?></h4>
                                <p class="card-text">
                                    <?php if ($selected_department): ?>
                                        เวรวันนี้ (แผนก)
                                    <?php else: ?>
                                        เวรวันนี้
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-day fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title"><?php echo number_format($stats['week_schedules']); ?></h4>
                                <p class="card-text">
                                    <?php if ($selected_department): ?>
                                        เวรสัปดาห์นี้ (แผนก)
                                    <?php else: ?>
                                        เวรสัปดาห์นี้
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-week fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Schedule -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="mb-2 mb-md-0">
                                <h6 class="card-title mb-1">
                                    <i class="fas fa-calendar-day me-1"></i>
                                    ตารางเวร
                                    <?php if ($selected_date == date('Y-m-d')): ?>
                                        วันนี้
                                    <?php else: ?>
                                        <?php echo getThaiDayName($selected_date); ?>
                                    <?php endif; ?>
                                </h6>
                                <small class="text-muted">
                                    <?php echo getThaiDayName($selected_date) . ' ' . formatThaiDate($selected_date); ?>
                                    <?php if ($selected_department): ?>
                                        <?php
                                        $dept_name = '';
                                        foreach ($departments as $dept) {
                                            if ($dept['id'] == $selected_department) {
                                                $dept_name = $dept['name'];
                                                break;
                                            }
                                        }
                                        ?>
                                        - แผนก<?php echo $dept_name; ?>
                                    <?php endif; ?>
                                </small>
                            </div>

                            <!-- Filters -->
                            <div class="d-flex gap-2 align-items-center flex-wrap">
                                <!-- Date Filter -->
                                <div class="d-flex gap-1 align-items-center">
                                    <small class="text-muted">วันที่:</small>
                                    <input type="date" id="dateFilter" class="form-control form-control-sm"
                                           value="<?php echo $selected_date; ?>"
                                           onchange="filterByDate()" style="width: 140px;">

                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                                onclick="changeDate(-1)" title="วันก่อนหน้า">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                onclick="goToToday()" title="วันนี้"
                                                <?php echo ($selected_date == date('Y-m-d')) ? 'disabled' : ''; ?>>
                                            วันนี้
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                                onclick="changeDate(1)" title="วันถัดไป">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Department Filter -->
                                <div class="d-flex gap-1 align-items-center">
                                    <small class="text-muted">แผนก:</small>
                                    <select id="departmentFilter" class="form-select form-select-sm" onchange="filterByDepartment()" style="width: 160px;">
                                        <option value="">ทุกแผนก</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?php echo $dept['id']; ?>"
                                                    <?php echo $selected_department == $dept['id'] ? 'selected' : ''; ?>>
                                                <?php echo $dept['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>

                                    <?php if ($selected_department): ?>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDepartmentFilter()" title="แสดงทุกแผนก">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <!-- Expand/Collapse Controls -->
                                <?php if (!empty($today_schedules)): ?>
                                <div class="d-flex gap-1 align-items-center">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                onclick="expandAllDoctors()" title="ขยายทั้งหมด">
                                            <i class="fas fa-expand-alt"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                onclick="collapseAllDoctors()" title="ยุบทั้งหมด">
                                            <i class="fas fa-compress-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!$selected_department): ?>
                            <!-- แสดงเมื่อยังไม่ได้เลือกแผนก -->
                            <div class="text-center py-4">
                                <i class="fas fa-building fa-2x text-primary mb-2"></i>
                                <h6 class="text-muted mb-2">เลือกแผนกเพื่อดูตารางเวรวันนี้</h6>
                                <p class="text-muted small mb-3">กรุณาเลือกแผนกจากเมนูด้านบนเพื่อดูตารางเวรของแพทย์ในแผนกนั้น</p>

                                <!-- แสดงรายการแผนกให้เลือก -->
                                <div class="row justify-content-center">
                                    <?php foreach ($departments as $dept): ?>
                                        <div class="col-md-4 col-lg-3 mb-2">
                                            <div class="card department-card h-100" onclick="selectDepartment(<?php echo $dept['id']; ?>)" style="cursor: pointer;">
                                                <div class="card-body text-center py-3">
                                                    <i class="fas fa-building fa-lg text-primary mb-1"></i>
                                                    <h6 class="card-title mb-1"><?php echo $dept['name']; ?></h6>
                                                    <small class="text-muted">คลิกเพื่อดูตารางเวร</small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php elseif (empty($today_schedules)): ?>
                            <!-- แสดงเมื่อเลือกแผนกแล้วแต่ไม่มีตารางเวร -->
                            <div class="text-center py-3">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                                <?php
                                $dept_name = '';
                                foreach ($departments as $dept) {
                                    if ($dept['id'] == $selected_department) {
                                        $dept_name = $dept['name'];
                                        break;
                                    }
                                }
                                ?>
                                <h6 class="text-muted">ไม่มีตารางเวรสำหรับแผนก<?php echo $dept_name; ?></h6>
                                <p class="text-muted small">ยังไม่มีการจัดตารางเวรสำหรับแผนก<?php echo $dept_name; ?>ในวันนี้</p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDepartmentFilter()">
                                        <i class="fas fa-eye me-1"></i>เลือกแผนกอื่น
                                    </button>
                                    <?php if (!$auth->isLoggedIn()): ?>
                                        <a href="login.php" class="btn btn-primary btn-sm">
                                            <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบเพื่อจัดการ
                                        </a>
                                    <?php else: ?>
                                        <a href="schedule.php?action=add" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>เพิ่มตารางเวร
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- แสดงตารางเวรแบบจัดกลุ่มตามแพทย์ -->
                            <div class="schedule-groups index-schedule-container">
                                <?php
                                // จัดกลุ่มตารางเวรตามแพทย์
                                $doctors_schedules = [];
                                foreach ($today_schedules as $schedule) {
                                    $doctor_key = $schedule['doctor_id'];
                                    if (!isset($doctors_schedules[$doctor_key])) {
                                        $doctors_schedules[$doctor_key] = [
                                            'doctor_info' => $schedule,
                                            'shifts' => []
                                        ];
                                    }
                                    $doctors_schedules[$doctor_key]['shifts'][] = $schedule;
                                }

                                // เรียงลำดับตามชื่อแพทย์
                                uasort($doctors_schedules, function($a, $b) {
                                    return strcmp($a['doctor_info']['fullname'], $b['doctor_info']['fullname']);
                                });
                                ?>

                                <?php foreach ($doctors_schedules as $doctor_group):
                                    $doctor_info = $doctor_group['doctor_info'];
                                    $shifts = $doctor_group['shifts'];

                                    // เรียงลำดับเวรตามเวลา
                                    usort($shifts, function($a, $b) {
                                        return strcmp($a['start_time'], $b['start_time']);
                                    });
                                ?>
                                    <div class="doctor-card" id="doctor-card-<?php echo $doctor_info['doctor_id']; ?>">
                                        <div class="doctor-header" onclick="toggleDoctorShifts(<?php echo $doctor_info['doctor_id']; ?>)" style="cursor: pointer;">
                                            <div class="doctor-info">
                                                <div class="doctor-name">
                                                    <i class="fas fa-user-md me-2"></i>
                                                    <?php echo $doctor_info['fullname']; ?>
                                                    <i class="fas fa-chevron-down ms-2 expand-icon" id="icon-<?php echo $doctor_info['doctor_id']; ?>"></i>
                                                </div>
                                                <div class="doctor-code">
                                                    รหัส: <?php echo $doctor_info['doctor_code']; ?>
                                                </div>
                                                <?php if ($doctor_info['department_name']): ?>
                                                    <div class="doctor-department">
                                                        <i class="fas fa-building me-1"></i>
                                                        <?php echo $doctor_info['department_name']; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="shifts-count">
                                                <?php echo count($shifts); ?> เวร
                                            </div>
                                        </div>

                                        <div class="shifts-list" id="shifts-<?php echo $doctor_info['doctor_id']; ?>" style="display: none;">
                                            <?php foreach ($shifts as $shift): ?>
                                                <div class="shift-item" style="border-left: 4px solid <?php echo $shift['color']; ?>;">
                                                    <div class="shift-content">
                                                        <div class="shift-time-info">
                                                            <div class="shift-time">
                                                                <?php echo formatTime($shift['start_time']) . ' - ' . formatTime($shift['end_time']); ?>
                                                            </div>
                                                            <div class="shift-name" style="color: <?php echo $shift['color']; ?>;">
                                                                <?php echo $shift['shift_name']; ?>
                                                            </div>
                                                        </div>
                                                        <?php if ($shift['notes']): ?>
                                                            <div class="shift-note">
                                                                <i class="fas fa-sticky-note me-1"></i>
                                                                <?php echo htmlspecialchars($shift['notes']); ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <!-- Action Buttons (แสดงเฉพาะเมื่อ login แล้ว) -->
                                                    <?php if ($auth->isLoggedIn()): ?>
                                                        <div class="shift-actions">
                                                            <div class="btn-group" role="group">
                                                                <button type="button" class="btn btn-outline-primary btn-sm"
                                                                        onclick="editSchedule(<?php echo $shift['id']; ?>)"
                                                                        title="แก้ไขเวร">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>

                                                                <div class="btn-group" role="group">
                                                                    <button type="button" class="btn btn-outline-warning btn-sm dropdown-toggle"
                                                                            data-bs-toggle="dropdown"
                                                                            title="การลา">
                                                                        <i class="fas fa-calendar-times"></i>
                                                                    </button>
                                                                    <ul class="dropdown-menu">
                                                                        <li><a class="dropdown-item leave-action" href="#"
                                                                               data-action="addNote"
                                                                               data-schedule-id="<?php echo $shift['id']; ?>"
                                                                               data-doctor-name="<?php echo htmlspecialchars($doctor_info['fullname']); ?>"
                                                                               data-shift-name="<?php echo htmlspecialchars($shift['shift_name']); ?>">
                                                                            <i class="fas fa-sticky-note me-2"></i>เพิ่มหมายเหตุการลา
                                                                        </a></li>
                                                                        <li><a class="dropdown-item leave-action" href="#"
                                                                               data-action="leaveSingle"
                                                                               data-doctor-id="<?php echo $doctor_info['doctor_id']; ?>"
                                                                               data-doctor-name="<?php echo htmlspecialchars($doctor_info['fullname']); ?>"
                                                                               data-selected-date="<?php echo $selected_date; ?>">
                                                                            <i class="fas fa-calendar-day me-2"></i>ลาวันนี้
                                                                        </a></li>
                                                                        <li><a class="dropdown-item leave-action" href="#"
                                                                               data-action="leaveRange"
                                                                               data-doctor-id="<?php echo $doctor_info['doctor_id']; ?>"
                                                                               data-doctor-name="<?php echo htmlspecialchars($doctor_info['fullname']); ?>">
                                                                            <i class="fas fa-calendar-week me-2"></i>ลาช่วงเวลา
                                                                        </a></li>
                                                                        <li><hr class="dropdown-divider"></li>
                                                                        <li><a class="dropdown-item leave-action" href="#"
                                                                               data-action="findReplacement"
                                                                               data-schedule-id="<?php echo $shift['id']; ?>"
                                                                               data-doctor-name="<?php echo htmlspecialchars($doctor_info['fullname']); ?>"
                                                                               data-shift-name="<?php echo htmlspecialchars($shift['shift_name']); ?>">
                                                                            <i class="fas fa-user-friends me-2"></i>หาแพทย์ทดแทน
                                                                        </a></li>
                                                                    </ul>
                                                                </div>

                                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                                        onclick="deleteSchedule(<?php echo $shift['id']; ?>, '<?php echo addslashes($doctor_info['fullname']); ?>', '<?php echo $shift['shift_name']; ?>')"
                                                                        title="ลบเวร">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Public Information (แสดงเฉพาะเมื่อยังไม่ login) -->
        <?php if (!$auth->isLoggedIn()): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            ข้อมูลสำหรับผู้ป่วยและญาติ
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <div class="alert alert-info py-2">
                                    <h6 class="mb-1"><i class="fas fa-clock me-1"></i>เวลาทำการ</h6>
                                    <p class="mb-0 small">
                                        <strong>เวรเช้า:</strong> 08:00 - 16:00<br>
                                        <strong>เวรบ่าย:</strong> 16:00 - 24:00<br>
                                        <strong>เวรดึก:</strong> 00:00 - 08:00
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="alert alert-warning py-2">
                                    <h6 class="mb-1"><i class="fas fa-exclamation-triangle me-1"></i>หมายเหตุ</h6>
                                    <p class="mb-0 small">
                                        ตารางเวรอาจมีการเปลี่ยนแปลงตามความเหมาะสม<br>
                                        กรุณาติดต่อแผนกที่เกี่ยวข้องเพื่อยืนยันข้อมูล<br>
                                        <strong>โทร:</strong> 02-XXX-XXXX
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="login.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                เข้าสู่ระบบสำหรับเจ้าหน้าที่
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <!-- Quick Actions สำหรับผู้ใช้ที่ login แล้ว -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-bolt me-1"></i>
                            การดำเนินการด่วน
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="schedule.php?action=add" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-plus-circle me-1"></i>
                                    เพิ่มตารางเวร
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="doctors.php?action=add" class="btn btn-success btn-sm w-100">
                                    <i class="fas fa-user-plus me-1"></i>
                                    เพิ่มแพทย์ใหม่
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="reports.php?type=weekly" class="btn btn-info btn-sm w-100">
                                    <i class="fas fa-chart-line me-1"></i>
                                    รายงานสัปดาห์
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="schedule.php?view=calendar" class="btn btn-warning btn-sm w-100">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    ดูปฏิทิน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/script.js"></script>

    <script>
        // ฟังก์ชันกรองตามแผนก
        function filterByDepartment() {
            const departmentId = document.getElementById('departmentFilter').value;
            const currentUrl = new URL(window.location);

            if (departmentId) {
                currentUrl.searchParams.set('department', departmentId);
            } else {
                currentUrl.searchParams.delete('department');
            }

            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันล้างตัวกรองแผนก
        function clearDepartmentFilter() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('department');
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันเลือกแผนก
        function selectDepartment(departmentId) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('department', departmentId);
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันเลือกวันที่
        function filterByDate() {
            const dateValue = document.getElementById('dateFilter').value;
            if (dateValue) {
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('date', dateValue);
                window.location.href = currentUrl.toString();
            }
        }

        // ฟังก์ชันเปลี่ยนวันที่ (ก่อนหน้า/ถัดไป)
        function changeDate(days) {
            const currentDate = new Date('<?php echo $selected_date; ?>');
            currentDate.setDate(currentDate.getDate() + days);

            const year = currentDate.getFullYear();
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');
            const newDate = `${year}-${month}-${day}`;

            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('date', newDate);
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชันไปวันนี้
        function goToToday() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('date', '<?php echo date('Y-m-d'); ?>');
            window.location.href = currentUrl.toString();
        }

        // ฟังก์ชัน toggle การแสดงเวรของแพทย์
        function toggleDoctorShifts(doctorId) {
            const shiftsContainer = document.getElementById('shifts-' + doctorId);
            const expandIcon = document.getElementById('icon-' + doctorId);
            const doctorCard = document.getElementById('doctor-card-' + doctorId);

            if (shiftsContainer.style.display === 'none' || shiftsContainer.style.display === '') {
                // แสดงเวร
                shiftsContainer.style.display = 'block';
                expandIcon.classList.remove('fa-chevron-down');
                expandIcon.classList.add('fa-chevron-up');
                doctorCard.classList.add('expanded');

                // เพิ่ม animation
                shiftsContainer.style.opacity = '0';
                shiftsContainer.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    shiftsContainer.style.transition = 'all 0.3s ease';
                    shiftsContainer.style.opacity = '1';
                    shiftsContainer.style.transform = 'translateY(0)';
                }, 10);
            } else {
                // ซ่อนเวร
                shiftsContainer.style.transition = 'all 0.3s ease';
                shiftsContainer.style.opacity = '0';
                shiftsContainer.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    shiftsContainer.style.display = 'none';
                    expandIcon.classList.remove('fa-chevron-up');
                    expandIcon.classList.add('fa-chevron-down');
                    doctorCard.classList.remove('expanded');
                }, 300);
            }
        }

        // ฟังก์ชันขยายทุกแพทย์
        function expandAllDoctors() {
            const doctorCards = document.querySelectorAll('.doctor-card');
            doctorCards.forEach(card => {
                const doctorId = card.id.replace('doctor-card-', '');
                const shiftsContainer = document.getElementById('shifts-' + doctorId);
                const expandIcon = document.getElementById('icon-' + doctorId);

                if (shiftsContainer.style.display === 'none' || shiftsContainer.style.display === '') {
                    shiftsContainer.style.display = 'block';
                    expandIcon.classList.remove('fa-chevron-down');
                    expandIcon.classList.add('fa-chevron-up');
                    card.classList.add('expanded');
                }
            });
        }

        // ฟังก์ชันยุบทุกแพทย์
        function collapseAllDoctors() {
            const doctorCards = document.querySelectorAll('.doctor-card');
            doctorCards.forEach(card => {
                const doctorId = card.id.replace('doctor-card-', '');
                const shiftsContainer = document.getElementById('shifts-' + doctorId);
                const expandIcon = document.getElementById('icon-' + doctorId);

                if (shiftsContainer.style.display === 'block') {
                    shiftsContainer.style.display = 'none';
                    expandIcon.classList.remove('fa-chevron-up');
                    expandIcon.classList.add('fa-chevron-down');
                    card.classList.remove('expanded');
                }
            });
        }

        // ฟังก์ชันแก้ไขเวร
        function editSchedule(scheduleId) {
            // เปิดหน้าแก้ไขเวรในแท็บใหม่
            window.open('schedule.php?action=edit&id=' + scheduleId, '_blank');
        }

        // ฟังก์ชันลบเวร
        function deleteSchedule(scheduleId, doctorName, shiftName) {
            if (confirm(`คุณต้องการลบเวร "${shiftName}" ของ "${doctorName}" หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้`)) {
                // แสดง loading
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'position-fixed top-50 start-50 translate-middle bg-white p-4 rounded shadow';
                loadingDiv.style.zIndex = '9999';
                loadingDiv.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">กำลังลบ...</span>
                        </div>
                        <div class="mt-2">กำลังลบเวร...</div>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // ส่งคำขอลบ
                fetch('schedule.php?action=delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'id=' + scheduleId
                })
                .then(response => response.json())
                .then(data => {
                    document.body.removeChild(loadingDiv);

                    if (data.success) {
                        // แสดงข้อความสำเร็จ
                        const successDiv = document.createElement('div');
                        successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                        successDiv.style.top = '20px';
                        successDiv.style.right = '20px';
                        successDiv.style.zIndex = '9999';
                        successDiv.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i>ลบเวรเรียบร้อยแล้ว
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.body.appendChild(successDiv);

                        // รีเฟรชหน้าหลังจาก 1.5 วินาที
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบเวรได้'));
                    }
                })
                .catch(error => {
                    document.body.removeChild(loadingDiv);
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
                });
            }
        }

        // ฟังก์ชันเพิ่มหมายเหตุ (สำหรับแพทย์ลา)
        function addLeaveNote(scheduleId, doctorName, shiftName) {
            const note = prompt(`เพิ่มหมายเหตุสำหรับเวร "${shiftName}" ของ "${doctorName}":\n\nตัวอย่าง: ลาป่วย, ลากิจ, เปลี่ยนเวร`, '');

            if (note !== null && note.trim() !== '') {
                // แสดง loading
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'position-fixed top-50 start-50 translate-middle bg-white p-4 rounded shadow';
                loadingDiv.style.zIndex = '9999';
                loadingDiv.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">กำลังบันทึก...</span>
                        </div>
                        <div class="mt-2">กำลังบันทึกหมายเหตุ...</div>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // ส่งคำขออัพเดทหมายเหตุ
                fetch('schedule.php?action=update_note', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'id=' + scheduleId + '&notes=' + encodeURIComponent(note.trim())
                })
                .then(response => response.json())
                .then(data => {
                    document.body.removeChild(loadingDiv);

                    if (data.success) {
                        // แสดงข้อความสำเร็จ
                        const successDiv = document.createElement('div');
                        successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                        successDiv.style.top = '20px';
                        successDiv.style.right = '20px';
                        successDiv.style.zIndex = '9999';
                        successDiv.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i>บันทึกหมายเหตุเรียบร้อยแล้ว
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.body.appendChild(successDiv);

                        // รีเฟรชหน้าหลังจาก 1.5 วินาที
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถบันทึกหมายเหตุได้'));
                    }
                })
                .catch(error => {
                    document.body.removeChild(loadingDiv);
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
                });
            }
        }

        // ฟังก์ชันขอลา
        function requestLeave(doctorId, doctorName, leaveType, selectedDate = null) {
            console.log('requestLeave called:', {doctorId, doctorName, leaveType, selectedDate});

            let leaveData = {
                doctor_id: doctorId,
                doctor_name: doctorName,
                leave_type: leaveType
            };

            if (leaveType === 'single') {
                // ลาวันเดียว
                const leaveDate = selectedDate || prompt('กรุณาระบุวันที่ต้องการลา (YYYY-MM-DD):', '<?php echo $selected_date; ?>');
                if (!leaveDate) return;

                const reason = prompt(`ขอลาวันที่ ${leaveDate} สำหรับ "${doctorName}"\n\nกรุณาระบุเหตุผลการลา:`, '');
                if (!reason) return;

                leaveData.leave_date = leaveDate;
                leaveData.reason = reason;

                processLeaveRequest(leaveData);

            } else if (leaveType === 'range') {
                // ลาช่วงเวลา
                showLeaveRangeModal(doctorId, doctorName);
            }
        }

        // ฟังก์ชันแสดง Modal สำหรับลาช่วงเวลา
        function showLeaveRangeModal(doctorId, doctorName) {
            const modalHtml = `
                <div class="modal fade" id="leaveRangeModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-calendar-week me-2"></i>
                                    ขอลาช่วงเวลา - ${doctorName}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="leaveRangeForm">
                                    <div class="mb-3">
                                        <label class="form-label">วันที่เริ่มต้น</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">วันที่สิ้นสุด</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">ประเภทการลา</label>
                                        <select class="form-select" id="leaveTypeSelect" required>
                                            <option value="">เลือกประเภทการลา</option>
                                            <option value="ลาป่วย">ลาป่วย</option>
                                            <option value="ลากิจ">ลากิจ</option>
                                            <option value="ลาพักร้อน">ลาพักร้อน</option>
                                            <option value="ลาคลอด">ลาคลอด</option>
                                            <option value="ลาฉุกเฉิน">ลาฉุกเฉิน</option>
                                            <option value="อื่นๆ">อื่นๆ</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">เหตุผลการลา</label>
                                        <textarea class="form-control" id="leaveReason" rows="3" required
                                                  placeholder="กรุณาระบุเหตุผลการลาอย่างละเอียด"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="findReplacement">
                                            <label class="form-check-label" for="findReplacement">
                                                ต้องการให้ระบบช่วยหาแพทย์ทดแทน
                                            </label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                <button type="button" class="btn btn-primary" onclick="submitLeaveRange(${doctorId}, '${doctorName}')">
                                    <i class="fas fa-paper-plane me-1"></i>ส่งคำขอลา
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // เพิ่ม modal เข้าไปใน body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // แสดง modal
            const modal = new bootstrap.Modal(document.getElementById('leaveRangeModal'));
            modal.show();

            // ลบ modal เมื่อปิด
            document.getElementById('leaveRangeModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });

            // ตั้งค่าวันที่เริ่มต้น
            document.getElementById('startDate').value = '<?php echo $selected_date; ?>';
            document.getElementById('endDate').value = '<?php echo $selected_date; ?>';
        }

        // ฟังก์ชันส่งคำขอลาช่วงเวลา
        function submitLeaveRange(doctorId, doctorName) {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const leaveType = document.getElementById('leaveTypeSelect').value;
            const reason = document.getElementById('leaveReason').value;
            const findReplacement = document.getElementById('findReplacement').checked;

            if (!startDate || !endDate || !leaveType || !reason.trim()) {
                alert('กรุณากรอกข้อมูลให้ครบถ้วน');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                alert('วันที่เริ่มต้นต้องไม่เกินวันที่สิ้นสุด');
                return;
            }

            const leaveData = {
                doctor_id: doctorId,
                doctor_name: doctorName,
                leave_type: 'range',
                start_date: startDate,
                end_date: endDate,
                leave_category: leaveType,
                reason: reason,
                find_replacement: findReplacement
            };

            // ปิด modal
            bootstrap.Modal.getInstance(document.getElementById('leaveRangeModal')).hide();

            processLeaveRequest(leaveData);
        }

        // ฟังก์ชันประมวลผลคำขอลา
        function processLeaveRequest(leaveData) {
            // แสดง loading
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'position-fixed top-50 start-50 translate-middle bg-white p-4 rounded shadow';
            loadingDiv.style.zIndex = '9999';
            loadingDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">กำลังส่งคำขอลา...</span>
                    </div>
                    <div class="mt-2">กำลังส่งคำขอลา...</div>
                </div>
            `;
            document.body.appendChild(loadingDiv);

            // ส่งคำขอลา
            fetch('leave.php?action=request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(leaveData)
            })
            .then(response => response.json())
            .then(data => {
                document.body.removeChild(loadingDiv);

                if (data.success) {
                    // แสดงข้อความสำเร็จ
                    const successDiv = document.createElement('div');
                    successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    successDiv.style.top = '20px';
                    successDiv.style.right = '20px';
                    successDiv.style.zIndex = '9999';
                    successDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>ส่งคำขอลาเรียบร้อยแล้ว
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(successDiv);

                    // รีเฟรชหน้าหลังจาก 2 วินาที
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถส่งคำขอลาได้'));
                }
            })
            .catch(error => {
                document.body.removeChild(loadingDiv);
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
            });
        }

        // ฟังก์ชันหาแพทย์ทดแทน
        function findReplacement(scheduleId, doctorName, shiftName) {
            if (confirm(`ต้องการหาแพทย์ทดแทนสำหรับเวร "${shiftName}" ของ "${doctorName}" หรือไม่?`)) {
                // เปิดหน้าหาแพทย์ทดแทนในแท็บใหม่
                window.open(`replacement.php?schedule_id=${scheduleId}&doctor_name=${encodeURIComponent(doctorName)}&shift_name=${encodeURIComponent(shiftName)}`, '_blank');
            }
        }

        // ฟังก์ชันแสดง loading effect
        function showLoadingEffect() {
            const statsCards = document.querySelector('.row.mb-4');
            const scheduleCard = document.querySelector('.card');

            if (statsCards) {
                statsCards.style.opacity = '0.6';
                statsCards.style.transition = 'opacity 0.3s ease';
            }

            if (scheduleCard) {
                scheduleCard.style.opacity = '0.6';
                scheduleCard.style.transition = 'opacity 0.3s ease';
            }

            // แสดง loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'text-center py-3';
            loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังโหลดข้อมูล...';
            loadingDiv.id = 'loading-indicator';

            const container = document.querySelector('.container');
            if (container && !document.getElementById('loading-indicator')) {
                container.appendChild(loadingDiv);
            }
        }

        // เพิ่ม animation เมื่อเปลี่ยนแผนกหรือวันที่
        document.addEventListener('DOMContentLoaded', function() {
            // เริ่มต้น Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Event listener สำหรับ department filter
            const departmentFilter = document.getElementById('departmentFilter');
            if (departmentFilter) {
                departmentFilter.addEventListener('change', function() {
                    showLoadingEffect();
                });
            }

            // Event listener สำหรับ date filter
            const dateFilter = document.getElementById('dateFilter');
            if (dateFilter) {
                dateFilter.addEventListener('change', function() {
                    showLoadingEffect();
                    filterByDate();
                });
            }

            // Event listener สำหรับปุ่มเปลี่ยนวันที่
            const prevDayBtn = document.querySelector('[onclick="changeDate(-1)"]');
            const nextDayBtn = document.querySelector('[onclick="changeDate(1)"]');
            const todayBtn = document.querySelector('[onclick="goToToday()"]');

            if (prevDayBtn) {
                prevDayBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    showLoadingEffect();
                    changeDate(-1);
                });
            }

            if (nextDayBtn) {
                nextDayBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    showLoadingEffect();
                    changeDate(1);
                });
            }

            if (todayBtn && !todayBtn.disabled) {
                todayBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    showLoadingEffect();
                    goToToday();
                });
            }

            // Event listener สำหรับปุ่มการลา
            document.addEventListener('click', function(e) {
                if (e.target.closest('.leave-action')) {
                    e.preventDefault();
                    const link = e.target.closest('.leave-action');
                    const action = link.getAttribute('data-action');

                    switch(action) {
                        case 'addNote':
                            const scheduleId = link.getAttribute('data-schedule-id');
                            const doctorName = link.getAttribute('data-doctor-name');
                            const shiftName = link.getAttribute('data-shift-name');
                            addLeaveNote(scheduleId, doctorName, shiftName);
                            break;

                        case 'leaveSingle':
                            const doctorId = link.getAttribute('data-doctor-id');
                            const doctorNameSingle = link.getAttribute('data-doctor-name');
                            const selectedDate = link.getAttribute('data-selected-date');
                            requestLeave(doctorId, doctorNameSingle, 'single', selectedDate);
                            break;

                        case 'leaveRange':
                            const doctorIdRange = link.getAttribute('data-doctor-id');
                            const doctorNameRange = link.getAttribute('data-doctor-name');
                            requestLeave(doctorIdRange, doctorNameRange, 'range');
                            break;

                        case 'findReplacement':
                            const scheduleIdReplace = link.getAttribute('data-schedule-id');
                            const doctorNameReplace = link.getAttribute('data-doctor-name');
                            const shiftNameReplace = link.getAttribute('data-shift-name');
                            findReplacement(scheduleIdReplace, doctorNameReplace, shiftNameReplace);
                            break;
                    }
                }
            });

            // เพิ่ม hover effect สำหรับ department cards
            const departmentCards = document.querySelectorAll('.department-card');
            departmentCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                });
            });
        });
    </script>
</body>
</html>
