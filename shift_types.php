<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอินและสิทธิ์
requireLogin();
requireModulePermission('shift_types', 'view');

$database = new Database();
$db = $database->getConnection();

$message = '';
$action = $_GET['action'] ?? 'list';
$shift_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_shift_type']) && $auth->hasModulePermission('shift_types', 'add')) {
        $query = "INSERT INTO shift_types (name, start_time, end_time, color, description) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);

        if ($stmt->execute([
            $_POST['name'],
            $_POST['start_time'],
            $_POST['end_time'],
            $_POST['color'],
            $_POST['description']
        ])) {
            $auth->logActivity('add_shift_type', 'shift_types', $db->lastInsertId(), null, $_POST);
            $message = showAlert('เพิ่มประเภทเวรเรียบร้อยแล้ว', 'success');
            $action = 'list';
        } else {
            $message = showAlert('เกิดข้อผิดพลาดในการเพิ่มประเภทเวร', 'error');
        }
    }
    
    if (isset($_POST['update_shift_type']) && $auth->hasModulePermission('shift_types', 'edit')) {
        // ดึงข้อมูลเก่าสำหรับ log
        $old_data_query = "SELECT * FROM shift_types WHERE id = ?";
        $old_stmt = $db->prepare($old_data_query);
        $old_stmt->execute([$shift_id]);
        $old_data = $old_stmt->fetch();

        $query = "UPDATE shift_types SET name=?, start_time=?, end_time=?, color=?, description=? WHERE id=?";
        $stmt = $db->prepare($query);

        if ($stmt->execute([
            $_POST['name'],
            $_POST['start_time'],
            $_POST['end_time'],
            $_POST['color'],
            $_POST['description'],
            $shift_id
        ])) {
            $auth->logActivity('update_shift_type', 'shift_types', $shift_id, $old_data, $_POST);
            $message = showAlert('อัปเดตประเภทเวรเรียบร้อยแล้ว', 'success');
            $action = 'list';
        } else {
            $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตประเภทเวร', 'error');
        }
    }
    
    if (isset($_POST['delete_shift_type']) && $auth->hasModulePermission('shift_types', 'delete')) {
        $delete_shift_id = $_POST['shift_id'] ?? null;

        if ($delete_shift_id) {
            // ดึงข้อมูลเก่าสำหรับ log
            $old_data_query = "SELECT * FROM shift_types WHERE id = ?";
            $old_stmt = $db->prepare($old_data_query);
            $old_stmt->execute([$delete_shift_id]);
            $old_data = $old_stmt->fetch();

            // ตรวจสอบว่ามีตารางเวรที่ใช้ประเภทเวรนี้หรือไม่
            $check_query = "SELECT COUNT(*) as count FROM schedules WHERE shift_type_id = ?";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->execute([$delete_shift_id]);
            $schedule_count = $check_stmt->fetch()['count'];

            if ($schedule_count > 0) {
                $message = showAlert('ไม่สามารถลบประเภทเวรได้ เนื่องจากมีตารางเวรที่ใช้ประเภทเวรนี้ (' . $schedule_count . ' ตารางเวร)', 'warning');
            } else {
                $query = "DELETE FROM shift_types WHERE id = ?";
                $stmt = $db->prepare($query);

                if ($stmt->execute([$delete_shift_id])) {
                    $auth->logActivity('delete_shift_type', 'shift_types', $delete_shift_id, $old_data);
                    $message = showAlert('ลบประเภทเวรเรียบร้อยแล้ว', 'success');
                } else {
                    $message = showAlert('เกิดข้อผิดพลาดในการลบประเภทเวร', 'error');
                }
            }
        } else {
            $message = showAlert('ไม่พบข้อมูลประเภทเวรที่ต้องการลบ', 'error');
        }
        $action = 'list';
    }
}

// Get shift type data for edit
$shift_type = null;
if ($action == 'edit' && $shift_id) {
    $query = "SELECT * FROM shift_types WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$shift_id]);
    $shift_type = $stmt->fetch();
}

// Get all shift types for list
if ($action == 'list') {
    $query = "SELECT st.*, COUNT(s.id) as schedule_count 
              FROM shift_types st 
              LEFT JOIN schedules s ON st.id = s.shift_type_id 
              GROUP BY st.id 
              ORDER BY st.start_time";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $shift_types = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ประเภทเวร - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-clock me-2"></i>
                            ประเภทเวร
                        </h1>
                        <p class="text-muted">จัดการประเภทเวรต่างๆ</p>
                    </div>
                    <?php if ($action == 'list'): ?>
                        <?php if ($auth->hasModulePermission('shift_types', 'add')): ?>
                            <a href="?action=add" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>เพิ่มประเภทเวรใหม่
                            </a>
                        <?php endif; ?>
                    <?php else: ?>
                        <a href="shift_types.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>กลับ
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($action == 'list'): ?>
            <!-- Shift Types List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        รายการประเภทเวร (<?php echo count($shift_types); ?> ประเภท)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($shift_types)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่มีข้อมูลประเภทเวร</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($shift_types as $shift): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100" style="border-left: 4px solid <?php echo $shift['color']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h5 class="card-title mb-0">
                                                    <span class="badge" style="background-color: <?php echo $shift['color']; ?>">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo htmlspecialchars($shift['name']); ?>
                                                    </span>
                                                </h5>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($auth->hasModulePermission('shift_types', 'edit')): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="?action=edit&id=<?php echo $shift['id']; ?>">
                                                                    <i class="fas fa-edit me-2"></i>แก้ไข
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if ($auth->hasModulePermission('shift_types', 'edit') && $auth->hasModulePermission('shift_types', 'delete')): ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                        <?php endif; ?>
                                                        <?php if ($auth->hasModulePermission('shift_types', 'delete')): ?>
                                                            <li>
                                                                <a class="dropdown-item text-danger" href="#" onclick="confirmDelete(<?php echo $shift['id']; ?>, '<?php echo htmlspecialchars($shift['name']); ?>')">
                                                                    <i class="fas fa-trash me-2"></i>ลบ
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-clock text-primary me-2"></i>
                                                    <strong>เวลา:</strong>
                                                    <span class="ms-2">
                                                        <?php echo formatTime($shift['start_time']) . ' - ' . formatTime($shift['end_time']); ?>
                                                    </span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-hourglass-half text-info me-2"></i>
                                                    <strong>ระยะเวลา:</strong>
                                                    <span class="ms-2">
                                                        <?php echo calculateWorkHours($shift['start_time'], $shift['end_time']); ?> ชั่วโมง
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <?php if ($shift['description']): ?>
                                                <p class="card-text text-muted mb-3">
                                                    <?php echo htmlspecialchars($shift['description']); ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-calendar-check me-1"></i>
                                                        <?php echo $shift['schedule_count']; ?> ตารางเวร
                                                    </span>
                                                </div>
                                                <small class="text-muted">
                                                    สร้างเมื่อ <?php echo formatThaiDate($shift['created_at']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action == 'add' || $action == 'edit'): ?>
            <!-- Add/Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-<?php echo $action == 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action == 'add' ? 'เพิ่มประเภทเวรใหม่' : 'แก้ไขประเภทเวร'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ชื่อประเภทเวร <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" value="<?php echo htmlspecialchars($shift_type['name'] ?? ''); ?>" required placeholder="เช่น เวรเช้า">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">สี <span class="text-danger">*</span></label>
                                <input type="color" class="form-control form-control-color" name="color" value="<?php echo $shift_type['color'] ?? '#007bff'; ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">เวลาเริ่มต้น <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" name="start_time" value="<?php echo $shift_type['start_time'] ?? ''; ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">เวลาสิ้นสุด <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" name="end_time" value="<?php echo $shift_type['end_time'] ?? ''; ?>" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">รายละเอียด</label>
                                <textarea class="form-control" name="description" rows="3" placeholder="รายละเอียดของประเภทเวร"><?php echo htmlspecialchars($shift_type['description'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <!-- Preview -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label">ตัวอย่าง</label>
                                <div class="p-3 border rounded">
                                    <span class="badge preview-badge" style="background-color: <?php echo $shift_type['color'] ?? '#007bff'; ?>">
                                        <i class="fas fa-clock me-1"></i>
                                        <span id="preview-name"><?php echo htmlspecialchars($shift_type['name'] ?? 'ชื่อประเภทเวร'); ?></span>
                                    </span>
                                    <span class="ms-2" id="preview-time">
                                        <?php 
                                        if ($shift_type) {
                                            echo formatTime($shift_type['start_time']) . ' - ' . formatTime($shift_type['end_time']);
                                        } else {
                                            echo '00:00 - 00:00';
                                        }
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="shift_types.php" class="btn btn-secondary">ยกเลิก</a>
                            <button type="submit" name="<?php echo $action == 'add' ? 'add_shift_type' : 'update_shift_type'; ?>" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?php echo $action == 'add' ? 'เพิ่มประเภทเวร' : 'บันทึกการแก้ไข'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณต้องการลบประเภทเวร <strong id="shiftTypeName"></strong> หรือไม่?</p>
                    <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถยกเลิกได้</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" id="deleteId" name="shift_id">
                        <button type="submit" name="delete_shift_type" class="btn btn-danger">ลบประเภทเวร</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteId').value = id;
            document.getElementById('shiftTypeName').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Live preview
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.querySelector('input[name="name"]');
            const colorInput = document.querySelector('input[name="color"]');
            const startTimeInput = document.querySelector('input[name="start_time"]');
            const endTimeInput = document.querySelector('input[name="end_time"]');
            
            const previewBadge = document.querySelector('.preview-badge');
            const previewName = document.getElementById('preview-name');
            const previewTime = document.getElementById('preview-time');

            function updatePreview() {
                if (nameInput && previewName) {
                    previewName.textContent = nameInput.value || 'ชื่อประเภทเวร';
                }
                if (colorInput && previewBadge) {
                    previewBadge.style.backgroundColor = colorInput.value;
                }
                if (startTimeInput && endTimeInput && previewTime) {
                    const startTime = startTimeInput.value || '00:00';
                    const endTime = endTimeInput.value || '00:00';
                    previewTime.textContent = startTime + ' - ' + endTime;
                }
            }

            if (nameInput) nameInput.addEventListener('input', updatePreview);
            if (colorInput) colorInput.addEventListener('input', updatePreview);
            if (startTimeInput) startTimeInput.addEventListener('input', updatePreview);
            if (endTimeInput) endTimeInput.addEventListener('input', updatePreview);
        });
    </script>
</body>
</html>
