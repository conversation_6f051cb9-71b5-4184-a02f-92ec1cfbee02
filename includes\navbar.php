<?php
// ตรวจสอบว่ามีการเรียกใช้ auth.php แล้วหรือไม่
if (!isset($auth)) {
    require_once 'auth.php';
}

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$current_user = $auth->getCurrentUser();
$user_permissions = $auth->getCurrentUserPermissions();
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-calendar-alt me-2"></i>
            ระบบตารางเวรแพทย์
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">
                        <i class="fas fa-home me-1"></i>หน้าหลัก
                    </a>
                </li>
                
                <?php if ($auth->hasModulePermission('doctors', 'view')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'doctors.php' ? 'active' : ''; ?>" href="doctors.php">
                        <i class="fas fa-user-md me-1"></i>จัดการแพทย์
                    </a>
                </li>
                <?php endif; ?>
                
                <?php if ($auth->hasModulePermission('schedules', 'view')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'schedule.php' ? 'active' : ''; ?>" href="schedule.php">
                        <i class="fas fa-calendar-check me-1"></i>จัดตารางเวร
                    </a>
                </li>
                <?php endif; ?>
                
                <?php if ($auth->hasModulePermission('departments', 'view')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'departments.php' ? 'active' : ''; ?>" href="departments.php">
                        <i class="fas fa-building me-1"></i>จัดการแผนก
                    </a>
                </li>
                <?php endif; ?>
                
                <?php if ($auth->hasModulePermission('shift_types', 'view')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'shift_types.php' ? 'active' : ''; ?>" href="shift_types.php">
                        <i class="fas fa-clock me-1"></i>ประเภทเวร
                    </a>
                </li>
                <?php endif; ?>
                
                <?php if ($auth->hasModulePermission('reports', 'view')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>" href="reports.php">
                        <i class="fas fa-chart-bar me-1"></i>รายงาน
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Admin Menu -->
                <?php if ($auth->hasModulePermission('admin', 'view') || $auth->hasModulePermission('settings', 'view') || $auth->hasModulePermission('logs', 'view')): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>ระบบ
                    </a>
                    <ul class="dropdown-menu">
                        <?php if ($auth->hasModulePermission('admin', 'view')): ?>
                        <li>
                            <a class="dropdown-item" href="admin_users.php">
                                <i class="fas fa-users me-2"></i>จัดการผู้ใช้
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if ($auth->hasModulePermission('settings', 'view')): ?>
                        <li>
                            <a class="dropdown-item" href="system_settings.php">
                                <i class="fas fa-cogs me-2"></i>ตั้งค่าระบบ
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if ($auth->hasModulePermission('logs', 'view')): ?>
                        <li>
                            <a class="dropdown-item" href="activity_logs.php">
                                <i class="fas fa-history me-2"></i>บันทึกการใช้งาน
                            </a>
                        </li>
                        <?php endif; ?>
                        
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="backup.php">
                                <i class="fas fa-download me-2"></i>สำรองข้อมูล
                            </a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
            
            <!-- User Menu -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span style="font-size: 0.85rem;"><?php echo htmlspecialchars($_SESSION['full_name'] ?? 'ผู้ใช้'); ?></span>
                        <span class="badge bg-light text-dark ms-1" style="font-size: 0.65rem;">
                            <?php
                            $role_names = [
                                'super_admin' => 'Super Admin',
                                'admin' => 'Admin',
                                'staff' => 'Staff',
                                'viewer' => 'Viewer'
                            ];
                            echo $role_names[$_SESSION['role']] ?? $_SESSION['role'];
                            ?>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <h6 class="dropdown-header" style="font-size: 0.8rem;">
                                <i class="fas fa-user me-2"></i>
                                <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                            </h6>
                        </li>
                        <li>
                            <span class="dropdown-item-text">
                                <small class="text-muted">
                                    <?php if (isset($_SESSION['department_name']) && $_SESSION['department_name']): ?>
                                        <i class="fas fa-building me-1"></i>
                                        <?php echo htmlspecialchars($_SESSION['department_name']); ?>
                                    <?php else: ?>
                                        <i class="fas fa-user me-1"></i>
                                        ไม่ระบุแผนก
                                    <?php endif; ?>
                                </small>
                            </span>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>แก้ไขโปรไฟล์
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="change_password.php">
                                <i class="fas fa-key me-2"></i>เปลี่ยนรหัสผ่าน
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Breadcrumb (Optional) -->
<?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
<nav aria-label="breadcrumb" class="bg-light">
    <div class="container">
        <ol class="breadcrumb mb-0 py-2">
            <li class="breadcrumb-item">
                <a href="index.php" class="text-decoration-none">
                    <i class="fas fa-home"></i> หน้าหลัก
                </a>
            </li>
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index == count($breadcrumbs) - 1): ?>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo htmlspecialchars($breadcrumb['title']); ?>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo $breadcrumb['url']; ?>" class="text-decoration-none">
                            <?php echo htmlspecialchars($breadcrumb['title']); ?>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>

<!-- Alert for session messages -->
<?php if (isset($_SESSION['message'])): ?>
<div class="container mt-3">
    <div class="alert alert-<?php echo $_SESSION['message_type'] ?? 'info'; ?> alert-dismissible fade show" role="alert">
        <?php echo htmlspecialchars($_SESSION['message']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
<?php 
unset($_SESSION['message']);
unset($_SESSION['message_type']);
endif; 
?>
