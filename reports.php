<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอินและสิทธิ์
requireLogin();
requireModulePermission('reports', 'view');

$database = new Database();
$db = $database->getConnection();

$report_type = $_GET['type'] ?? 'summary';
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month
$doctor_id = $_GET['doctor_id'] ?? '';
$department_id = $_GET['department_id'] ?? '';

// Get doctors and departments for filters
$doctors_query = "SELECT d.*, dept.name as department_name FROM doctors d LEFT JOIN departments dept ON d.department_id = dept.id WHERE d.status = 'active' ORDER BY d.doctor_code";
$doctors_stmt = $db->prepare($doctors_query);
$doctors_stmt->execute();
$doctors = $doctors_stmt->fetchAll();

$departments_query = "SELECT * FROM departments ORDER BY name";
$departments_stmt = $db->prepare($departments_query);
$departments_stmt->execute();
$departments = $departments_stmt->fetchAll();

// Generate reports based on type
$report_data = [];
$report_title = '';

switch ($report_type) {
    case 'summary':
        $report_title = 'รายงานสรุปตารางเวร';
        
        // Get summary statistics
        $query = "SELECT 
                    COUNT(DISTINCT s.doctor_id) as total_doctors,
                    COUNT(s.id) as total_schedules,
                    COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_schedules,
                    COUNT(CASE WHEN s.status = 'cancelled' THEN 1 END) as cancelled_schedules,
                    COUNT(DISTINCT s.schedule_date) as total_days
                  FROM schedules s
                  WHERE s.schedule_date BETWEEN ? AND ?";
        
        $params = [$start_date, $end_date];
        
        if ($doctor_id) {
            $query .= " AND s.doctor_id = ?";
            $params[] = $doctor_id;
        }
        
        if ($department_id) {
            $query .= " AND EXISTS (SELECT 1 FROM doctors d WHERE d.id = s.doctor_id AND d.department_id = ?)";
            $params[] = $department_id;
        }
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $report_data['summary'] = $stmt->fetch();
        
        // Get schedules by shift type
        $query = "SELECT st.name, st.color, COUNT(s.id) as count
                  FROM shift_types st
                  LEFT JOIN schedules s ON st.id = s.shift_type_id 
                    AND s.schedule_date BETWEEN ? AND ?";
        
        $params = [$start_date, $end_date];
        
        if ($doctor_id) {
            $query .= " AND s.doctor_id = ?";
            $params[] = $doctor_id;
        }
        
        if ($department_id) {
            $query .= " AND EXISTS (SELECT 1 FROM doctors d WHERE d.id = s.doctor_id AND d.department_id = ?)";
            $params[] = $department_id;
        }
        
        $query .= " GROUP BY st.id ORDER BY st.start_time";
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $report_data['shift_types'] = $stmt->fetchAll();
        
        break;
        
    case 'doctor':
        $report_title = 'รายงานตารางเวรรายแพทย์';
        
        $query = "SELECT d.doctor_code, d.fullname, dept.name as department_name,
                         COUNT(s.id) as total_schedules,
                         COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_schedules,
                         COUNT(CASE WHEN s.status = 'cancelled' THEN 1 END) as cancelled_schedules,
                         SUM(CASE
                             WHEN st.start_time <= st.end_time THEN
                                 TIME_TO_SEC(TIMEDIFF(st.end_time, st.start_time)) / 3600
                             ELSE
                                 (TIME_TO_SEC(TIMEDIFF('24:00:00', st.start_time)) + TIME_TO_SEC(st.end_time)) / 3600
                         END) as total_hours
                  FROM doctors d
                  LEFT JOIN schedules s ON d.id = s.doctor_id AND s.schedule_date BETWEEN ? AND ?
                  LEFT JOIN shift_types st ON s.shift_type_id = st.id
                  LEFT JOIN departments dept ON d.department_id = dept.id
                  WHERE d.status = 'active'";
        
        $params = [$start_date, $end_date];
        
        if ($doctor_id) {
            $query .= " AND d.id = ?";
            $params[] = $doctor_id;
        }
        
        if ($department_id) {
            $query .= " AND d.department_id = ?";
            $params[] = $department_id;
        }
        
        $query .= " GROUP BY d.id ORDER BY d.doctor_code";
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $report_data['doctors'] = $stmt->fetchAll();
        
        break;
        
    case 'schedule':
        $report_title = 'รายงานตารางเวรรายละเอียด';
        
        $query = "SELECT s.schedule_date, d.doctor_code, d.first_name, d.last_name,
                         st.name as shift_name, st.start_time, st.end_time, st.color,
                         dept.name as department_name, s.status, s.notes
                  FROM schedules s
                  JOIN doctors d ON s.doctor_id = d.id
                  JOIN shift_types st ON s.shift_type_id = st.id
                  LEFT JOIN departments dept ON d.department_id = dept.id
                  WHERE s.schedule_date BETWEEN ? AND ?";
        
        $params = [$start_date, $end_date];
        
        if ($doctor_id) {
            $query .= " AND s.doctor_id = ?";
            $params[] = $doctor_id;
        }
        
        if ($department_id) {
            $query .= " AND d.department_id = ?";
            $params[] = $department_id;
        }
        
        $query .= " ORDER BY s.schedule_date, st.start_time, d.doctor_code";
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $report_data['schedules'] = $stmt->fetchAll();
        
        break;
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รายงาน - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-1">
                    <i class="fas fa-chart-bar me-2"></i>
                    รายงาน
                </h1>
                <p class="text-muted">รายงานและสถิติการจัดตารางเวรแพทย์</p>
            </div>
        </div>

        <!-- Report Type Selection -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    เลือกประเภทรายงานและตัวกรอง
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">ประเภทรายงาน</label>
                        <select class="form-select" name="type">
                            <option value="summary" <?php echo $report_type == 'summary' ? 'selected' : ''; ?>>รายงานสรุป</option>
                            <option value="doctor" <?php echo $report_type == 'doctor' ? 'selected' : ''; ?>>รายงานรายแพทย์</option>
                            <option value="schedule" <?php echo $report_type == 'schedule' ? 'selected' : ''; ?>>รายงานตารางเวร</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">วันที่เริ่มต้น</label>
                        <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">วันที่สิ้นสุด</label>
                        <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">แพทย์</label>
                        <select class="form-select" name="doctor_id">
                            <option value="">ทุกคน</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?php echo $doctor['id']; ?>" <?php echo $doctor_id == $doctor['id'] ? 'selected' : ''; ?>>
                                    <?php echo $doctor['doctor_code'] . ' - ' . $doctor['first_name'] . ' ' . $doctor['last_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">แผนก</label>
                        <select class="form-select" name="department_id">
                            <option value="">ทุกแผนก</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>" <?php echo $department_id == $dept['id'] ? 'selected' : ''; ?>>
                                    <?php echo $dept['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Report Content -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    <?php echo $report_title; ?>
                </h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel('reportTable', 'report.csv')">
                        <i class="fas fa-file-excel me-1"></i>Excel
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="printTable('reportTable')">
                        <i class="fas fa-print me-1"></i>พิมพ์
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">
                        ช่วงเวลา: <?php echo formatThaiDate($start_date) . ' - ' . formatThaiDate($end_date); ?>
                        <?php if ($doctor_id): ?>
                            | แพทย์: <?php 
                                $selected_doctor = array_filter($doctors, function($d) use ($doctor_id) { return $d['id'] == $doctor_id; });
                                $selected_doctor = reset($selected_doctor);
                                echo $selected_doctor['doctor_code'] . ' - ' . $selected_doctor['first_name'] . ' ' . $selected_doctor['last_name'];
                            ?>
                        <?php endif; ?>
                        <?php if ($department_id): ?>
                            | แผนก: <?php 
                                $selected_dept = array_filter($departments, function($d) use ($department_id) { return $d['id'] == $department_id; });
                                $selected_dept = reset($selected_dept);
                                echo $selected_dept['name'];
                            ?>
                        <?php endif; ?>
                    </small>
                </div>

                <?php if ($report_type == 'summary'): ?>
                    <!-- Summary Report -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo number_format($report_data['summary']['total_doctors']); ?></h4>
                                    <p class="mb-0">แพทย์ที่มีเวร</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo number_format($report_data['summary']['total_schedules']); ?></h4>
                                    <p class="mb-0">ตารางเวรทั้งหมด</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo number_format($report_data['summary']['completed_schedules']); ?></h4>
                                    <p class="mb-0">เวรที่เสร็จสิ้น</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo number_format($report_data['summary']['cancelled_schedules']); ?></h4>
                                    <p class="mb-0">เวรที่ยกเลิก</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6>สถิติตามประเภทเวร</h6>
                    <div class="table-responsive">
                        <table class="table table-striped" id="reportTable">
                            <thead>
                                <tr>
                                    <th>ประเภทเวร</th>
                                    <th>จำนวนเวร</th>
                                    <th>เปอร์เซ็นต์</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $total_shifts = array_sum(array_column($report_data['shift_types'], 'count'));
                                foreach ($report_data['shift_types'] as $shift): 
                                    $percentage = $total_shifts > 0 ? ($shift['count'] / $total_shifts) * 100 : 0;
                                ?>
                                    <tr>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo $shift['color']; ?>">
                                                <?php echo $shift['name']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo number_format($shift['count']); ?></td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" style="width: <?php echo $percentage; ?>%; background-color: <?php echo $shift['color']; ?>">
                                                    <?php echo number_format($percentage, 1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                <?php elseif ($report_type == 'doctor'): ?>
                    <!-- Doctor Report -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="reportTable">
                            <thead>
                                <tr>
                                    <th>รหัสแพทย์</th>
                                    <th>ชื่อ-นามสกุล</th>
                                    <th>แผนก</th>
                                    <th>จำนวนเวร</th>
                                    <th>เวรเสร็จสิ้น</th>
                                    <th>เวรยกเลิก</th>
                                    <th>ชั่วโมงรวม</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($report_data['doctors'] as $doctor): ?>
                                    <tr>
                                        <td><?php echo $doctor['doctor_code']; ?></td>
                                        <td><?php echo $doctor['first_name'] . ' ' . $doctor['last_name']; ?></td>
                                        <td><?php echo $doctor['department_name'] ?? '-'; ?></td>
                                        <td><?php echo number_format($doctor['total_schedules']); ?></td>
                                        <td><?php echo number_format($doctor['completed_schedules']); ?></td>
                                        <td><?php echo number_format($doctor['cancelled_schedules']); ?></td>
                                        <td><?php echo number_format($doctor['total_hours'] ?? 0, 1); ?> ชม.</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                <?php elseif ($report_type == 'schedule'): ?>
                    <!-- Schedule Report -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="reportTable">
                            <thead>
                                <tr>
                                    <th>วันที่</th>
                                    <th>แพทย์</th>
                                    <th>แผนก</th>
                                    <th>ประเภทเวร</th>
                                    <th>เวลา</th>
                                    <th>สถานะ</th>
                                    <th>หมายเหตุ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($report_data['schedules'] as $schedule): ?>
                                    <tr>
                                        <td><?php echo formatThaiDate($schedule['schedule_date']); ?></td>
                                        <td>
                                            <strong><?php echo $schedule['doctor_code']; ?></strong><br>
                                            <?php echo $schedule['first_name'] . ' ' . $schedule['last_name']; ?>
                                        </td>
                                        <td><?php echo $schedule['department_name'] ?? '-'; ?></td>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo $schedule['color']; ?>">
                                                <?php echo $schedule['shift_name']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatTime($schedule['start_time']) . ' - ' . formatTime($schedule['end_time']); ?></td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'scheduled' => 'bg-primary',
                                                'completed' => 'bg-success',
                                                'cancelled' => 'bg-danger'
                                            ];
                                            $statusText = [
                                                'scheduled' => 'กำหนดแล้ว',
                                                'completed' => 'เสร็จสิ้น',
                                                'cancelled' => 'ยกเลิก'
                                            ];
                                            ?>
                                            <span class="badge <?php echo $statusClass[$schedule['status']]; ?>">
                                                <?php echo $statusText[$schedule['status']]; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $schedule['notes'] ?? '-'; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
