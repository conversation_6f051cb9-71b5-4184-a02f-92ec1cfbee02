<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอินและสิทธิ์
requireLogin();
requireModulePermission('departments', 'view');

$database = new Database();
$db = $database->getConnection();

$message = '';
$action = $_GET['action'] ?? 'list';
$department_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_department']) && $auth->hasModulePermission('departments', 'add')) {
        $query = "INSERT INTO departments (name, description) VALUES (?, ?)";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute([
            $_POST['name'],
            $_POST['description']
        ])) {
            $message = showAlert('เพิ่มแผนกเรียบร้อยแล้ว', 'success');
            $action = 'list';
        } else {
            $message = showAlert('เกิดข้อผิดพลาดในการเพิ่มแผนก', 'error');
        }
    }
    
    if (isset($_POST['update_department'])) {
        $query = "UPDATE departments SET name=?, description=? WHERE id=?";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute([
            $_POST['name'],
            $_POST['description'],
            $department_id
        ])) {
            $message = showAlert('อัปเดตแผนกเรียบร้อยแล้ว', 'success');
            $action = 'list';
        } else {
            $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตแผนก', 'error');
        }
    }
    
    if (isset($_POST['delete_department'])) {
        // ตรวจสอบว่ามีแพทย์ในแผนกนี้หรือไม่
        $check_query = "SELECT COUNT(*) as count FROM doctors WHERE department_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$department_id]);
        $doctor_count = $check_stmt->fetch()['count'];
        
        if ($doctor_count > 0) {
            $message = showAlert('ไม่สามารถลบแผนกได้ เนื่องจากมีแพทย์ในแผนกนี้', 'warning');
        } else {
            $query = "DELETE FROM departments WHERE id = ?";
            $stmt = $db->prepare($query);
            
            if ($stmt->execute([$department_id])) {
                $message = showAlert('ลบแผนกเรียบร้อยแล้ว', 'success');
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการลบแผนก', 'error');
            }
        }
        $action = 'list';
    }
}

// Get department data for edit
$department = null;
if ($action == 'edit' && $department_id) {
    $query = "SELECT * FROM departments WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$department_id]);
    $department = $stmt->fetch();
}

// Get all departments for list
if ($action == 'list') {
    $query = "SELECT d.*, COUNT(doc.id) as doctor_count 
              FROM departments d 
              LEFT JOIN doctors doc ON d.id = doc.department_id 
              GROUP BY d.id 
              ORDER BY d.name";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $departments = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการแผนก - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-building me-2"></i>
                            จัดการแผนก
                        </h1>
                        <p class="text-muted">จัดการแผนกต่างๆ ในโรงพยาบาล</p>
                    </div>
                    <?php if ($action == 'list'): ?>
                        <a href="?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>เพิ่มแผนกใหม่
                        </a>
                    <?php else: ?>
                        <a href="departments.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>กลับ
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($action == 'list'): ?>
            <!-- Departments List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        รายการแผนก (<?php echo count($departments); ?> แผนก)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($departments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่มีข้อมูลแผนก</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($departments as $dept): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 department-card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h5 class="card-title mb-0">
                                                    <i class="fas fa-building text-primary me-2"></i>
                                                    <?php echo htmlspecialchars($dept['name']); ?>
                                                </h5>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="?action=edit&id=<?php echo $dept['id']; ?>">
                                                                <i class="fas fa-edit me-2"></i>แก้ไข
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#" onclick="confirmDelete(<?php echo $dept['id']; ?>, '<?php echo htmlspecialchars($dept['name']); ?>')">
                                                                <i class="fas fa-trash me-2"></i>ลบ
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            
                                            <?php if ($dept['description']): ?>
                                                <p class="card-text text-muted mb-3">
                                                    <?php echo htmlspecialchars($dept['description']); ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-user-md me-1"></i>
                                                        <?php echo $dept['doctor_count']; ?> แพทย์
                                                    </span>
                                                </div>
                                                <small class="text-muted">
                                                    สร้างเมื่อ <?php echo formatThaiDate($dept['created_at']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action == 'add' || $action == 'edit'): ?>
            <!-- Add/Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-<?php echo $action == 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action == 'add' ? 'เพิ่มแผนกใหม่' : 'แก้ไขข้อมูลแผนก'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ชื่อแผนก <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" value="<?php echo htmlspecialchars($department['name'] ?? ''); ?>" required placeholder="เช่น แผนกอายุรกรรม">
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">รายละเอียด</label>
                                <textarea class="form-control" name="description" rows="4" placeholder="รายละเอียดของแผนก"><?php echo htmlspecialchars($department['description'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="departments.php" class="btn btn-secondary">ยกเลิก</a>
                            <button type="submit" name="<?php echo $action == 'add' ? 'add_department' : 'update_department'; ?>" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?php echo $action == 'add' ? 'เพิ่มแผนก' : 'บันทึกการแก้ไข'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณต้องการลบแผนก <strong id="departmentName"></strong> หรือไม่?</p>
                    <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถยกเลิกได้</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" id="deleteId" name="department_id">
                        <button type="submit" name="delete_department" class="btn btn-danger">ลบแผนก</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteId').value = id;
            document.getElementById('departmentName').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
