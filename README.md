# ระบบตารางเวรแพทย์ (Doctor Schedule Management System)

ระบบจัดการตารางเวรแพทย์ที่พัฒนาด้วย PHP และ MySQL สำหรับโรงพยาบาลและสถานพยาบาล

## ฟีเจอร์หลัก

### 🏥 การจัดการข้อมูลพื้นฐาน
- **จัดการแพทย์**: เพิ่ม แก้ไข ลบข้อมูลแพทย์
- **จัดการแผนก**: จัดการแผนกต่างๆ ในโรงพยาบาล
- **จัดการประเภทเวร**: กำหนดประเภทเวร เวลา และสีประจำเวร

### 📅 การจัดตารางเวร
- **ปฏิทินตารางเวร**: แสดงตารางเวรในรูปแบบปฏิทิน
- **เพิ่มตารางเวร**: จัดตารางเวรให้แพทย์แต่ละคน
- **แก้ไขตารางเวร**: ปรับเปลี่ยนตารางเวรที่มีอยู่
- **ป้องกันการซ้ำซ้อน**: ระบบตรวจสอบการซ้ำซ้อนของเวร

### 📊 รายงานและสถิติ
- **รายงานสรุป**: สถิติการจัดเวรโดยรวม
- **รายงานรายแพทย์**: รายงานการทำงานของแพทย์แต่ละคน
- **รายงานตารางเวร**: รายละเอียดตารางเวรทั้งหมด
- **ส่งออกข้อมูล**: ส่งออกเป็น Excel และพิมพ์รายงาน

### 🎨 ส่วนติดต่อผู้ใช้
- **Responsive Design**: ใช้งานได้ทั้งคอมพิวเตอร์และมือถือ
- **ภาษาไทย**: รองรับภาษาไทยเต็มรูปแบบ
- **สีสันสวยงาม**: ใช้ Bootstrap 5 และ CSS ที่ออกแบบเฉพาะ

## ความต้องการของระบบ

### เซิร์ฟเวอร์
- **PHP**: เวอร์ชัน 7.4 หรือสูงกว่า
- **MySQL**: เวอร์ชัน 5.7 หรือสูงกว่า หรือ MariaDB 10.2+
- **Web Server**: Apache หรือ Nginx

### สำหรับการพัฒนา
- **XAMPP**: สำหรับการทดสอบในเครื่อง
- **MySQL Workbench**: สำหรับจัดการฐานข้อมูล (ไม่บังคับ)

## การติดตั้ง

### 1. ดาวน์โหลดและติดตั้ง XAMPP
1. ดาวน์โหลด XAMPP จาก [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. ติดตั้งและเปิด XAMPP Control Panel
3. เริ่มต้น Apache และ MySQL

### 2. วางไฟล์ระบบ
1. คัดลอกไฟล์ทั้งหมดไปยัง `C:\xampp\htdocs\appointment\`
2. หรือโฟลเดอร์ที่ต้องการในเว็บเซิร์ฟเวอร์

### 3. ตั้งค่าฐานข้อมูล
1. เปิดเบราว์เซอร์และไปที่ `http://localhost/appointment/test_connection.php`
2. ตรวจสอบการเชื่อมต่อ MySQL
3. หากเชื่อมต่อไม่ได้ ให้ไปที่ `http://localhost/appointment/setup_database.php`
4. กรอกข้อมูลการเชื่อมต่อฐานข้อมูล:
   - **Host**: localhost
   - **Username**: root
   - **Password**: (เว้นว่างสำหรับ XAMPP)
   - **Port**: 3306
5. คลิก "ตั้งค่าฐานข้อมูล"

### 4. เข้าใช้งานระบบ
เปิดเบราว์เซอร์และไปที่ `http://localhost/appointment/`

## โครงสร้างไฟล์

```
appointment/
├── config/
│   └── database.php          # การเชื่อมต่อฐานข้อมูล
├── assets/
│   ├── css/
│   │   └── style.css         # สไตล์ CSS
│   └── js/
│       └── script.js         # JavaScript
├── index.php                 # หน้าหลัก/แดชบอร์ด
├── doctors.php               # จัดการแพทย์
├── departments.php           # จัดการแผนก
├── shift_types.php           # จัดการประเภทเวร
├── schedule.php              # จัดตารางเวร
├── reports.php               # รายงาน
├── test_connection.php       # ทดสอบการเชื่อมต่อ
├── setup_database.php        # ตั้งค่าฐานข้อมูล
├── database.sql              # โครงสร้างฐานข้อมูล
└── README.md                 # คู่มือการใช้งาน
```

## การใช้งาน

### 1. หน้าแดชบอร์ด
- แสดงสถิติการจัดเวรโดยรวม
- ตารางเวรวันนี้
- การดำเนินการด่วน

### 2. จัดการแพทย์
- เพิ่มแพทย์ใหม่: กรอกข้อมูลรหัสแพทย์ ชื่อ-นามสกุล แผนก ฯลฯ
- แก้ไขข้อมูล: คลิกปุ่มแก้ไขในรายการแพทย์
- ค้นหาและกรอง: ใช้ช่องค้นหาและตัวกรองแผนก/สถานะ

### 3. จัดการแผนก
- เพิ่มแผนกใหม่: ระบุชื่อแผนกและรายละเอียด
- แก้ไขแผนก: คลิกเมนูจัดการในการ์ดแผนก
- ดูจำนวนแพทย์: แสดงจำนวนแพทย์ในแต่ละแผนก

### 4. จัดการประเภทเวร
- เพิ่มประเภทเวร: กำหนดชื่อ เวลาเริ่ม-สิ้นสุด และสี
- ดูตัวอย่าง: ระบบแสดงตัวอย่างการแสดงผลแบบเรียลไทม์
- คำนวณชั่วโมง: ระบบคำนวณจำนวนชั่วโมงทำงานอัตโนมัติ

### 5. จัดตารางเวร
- **มุมมองปฏิทิน**: ดูตารางเวรในรูปแบบปฏิทินรายเดือน
- **เพิ่มเวร**: เลือกแพทย์ ประเภทเวร และวันที่
- **แก้ไขเวร**: คลิกที่เวรในปฏิทินเพื่อแก้ไข
- **ป้องกันซ้ำซ้อน**: ระบบจะเตือนหากมีการจัดเวรซ้ำ

### 6. รายงาน
- **รายงานสรุป**: สถิติโดยรวมและกราฟแสดงสัดส่วนเวร
- **รายงานรายแพทย์**: จำนวนเวรและชั่วโมงทำงานของแต่ละคน
- **รายงานตารางเวร**: รายละเอียดตารางเวรทั้งหมด
- **ส่งออก**: ดาวน์โหลดเป็น CSV หรือพิมพ์

## การแก้ไขปัญหา

### ปัญหาการเชื่อมต่อฐานข้อมูล
1. ตรวจสอบว่า MySQL ทำงานใน XAMPP Control Panel
2. ตรวจสอบ username/password ใน `config/database.php`
3. ลองใช้ `setup_database.php` เพื่อตั้งค่าใหม่

### ปัญหาการแสดงผลภาษาไทย
1. ตรวจสอบ charset ของฐานข้อมูลเป็น utf8mb4
2. ตรวจสอบการตั้งค่า PHP charset

### ปัญหาการแสดงผลบนมือถือ
1. ตรวจสอบ viewport meta tag
2. ทดสอบใน responsive mode ของเบราว์เซอร์

## การพัฒนาต่อ

### ฟีเจอร์ที่สามารถเพิ่มได้
- ระบบ Login/Authentication
- การแจ้งเตือนผ่าน Email/SMS
- การขอเปลี่ยนเวรระหว่างแพทย์
- ระบบอนุมัติตารางเวร
- การส่งออกเป็น PDF
- API สำหรับแอปมือถือ

### การปรับแต่ง
- แก้ไขสีและธีมใน `assets/css/style.css`
- เพิ่มฟังก์ชัน JavaScript ใน `assets/js/script.js`
- ปรับแต่งการเชื่อมต่อฐานข้อมูลใน `config/database.php`

## การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ error log ของ PHP
2. ใช้ Developer Tools ของเบราว์เซอร์
3. ตรวจสอบ MySQL error log

## ใบอนุญาต

โปรเจกต์นี้เป็น Open Source สามารถนำไปใช้และพัฒนาต่อได้อย่างอิสระ

---

**หมายเหตุ**: ระบบนี้พัฒนาขึ้นเพื่อการศึกษาและใช้งานภายในองค์กร กรุณาทดสอบอย่างละเอียดก่อนนำไปใช้งานจริง
