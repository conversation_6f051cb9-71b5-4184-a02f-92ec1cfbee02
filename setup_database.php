<?php
// ตั้งค่าฐานข้อมูลและสร้างตารางใหม่

echo "<h2>ตั้งค่าฐานข้อมูลระบบตารางเวรแพทย์</h2>";

if ($_POST && isset($_POST['setup'])) {
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $port = $_POST['port'] ?? 3306;
    
    try {
        echo "<h3>กำลังตั้งค่าฐานข้อมูล...</h3>";
        
        // เชื่อมต่อ MySQL Server
        $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        echo "✅ เชื่อมต่อ MySQL Server สำเร็จ<br>";
        
        // ลบฐานข้อมูลเก่า (ถ้ามี)
        $pdo->exec("DROP DATABASE IF EXISTS doctor_schedule");
        echo "🗑️ ลบฐานข้อมูลเก่า (ถ้ามี)<br>";
        
        // สร้างฐานข้อมูลใหม่
        $pdo->exec("CREATE DATABASE doctor_schedule CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ สร้างฐานข้อมูล 'doctor_schedule' ใหม่<br>";
        
        // เชื่อมต่อกับฐานข้อมูลใหม่
        $dsn_with_db = "mysql:host=$host;port=$port;dbname=doctor_schedule;charset=utf8mb4";
        $pdo_db = new PDO($dsn_with_db, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        echo "✅ เชื่อมต่อฐานข้อมูลใหม่สำเร็จ<br>";
        
        // สร้างตารางทั้งหมด
        echo "<h4>สร้างตาราง:</h4>";
        
        // ตารางแผนก
        $pdo_db->exec("
            CREATE TABLE departments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "✅ สร้างตาราง departments<br>";
        
        // ตารางประเภทเวร
        $pdo_db->exec("
            CREATE TABLE shift_types (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                color VARCHAR(7) DEFAULT '#007bff',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "✅ สร้างตาราง shift_types<br>";
        
        // ตารางแพทย์
        $pdo_db->exec("
            CREATE TABLE doctors (
                id INT AUTO_INCREMENT PRIMARY KEY,
                doctor_code VARCHAR(20) UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE,
                phone VARCHAR(20),
                department_id INT,
                position VARCHAR(100),
                specialization VARCHAR(100),
                license_number VARCHAR(50),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
            )
        ");
        echo "✅ สร้างตาราง doctors<br>";
        
        // ตารางตารางเวร
        $pdo_db->exec("
            CREATE TABLE schedules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                doctor_id INT NOT NULL,
                shift_type_id INT NOT NULL,
                schedule_date DATE NOT NULL,
                notes TEXT,
                status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
                created_by VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
                FOREIGN KEY (shift_type_id) REFERENCES shift_types(id) ON DELETE CASCADE,
                UNIQUE KEY unique_doctor_date_shift (doctor_id, schedule_date, shift_type_id)
            )
        ");
        echo "✅ สร้างตาราง schedules<br>";
        
        // ตารางการขอเปลี่ยนเวร
        $pdo_db->exec("
            CREATE TABLE shift_change_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                from_doctor_id INT NOT NULL,
                to_doctor_id INT NOT NULL,
                schedule_id INT NOT NULL,
                reason TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reviewed_at TIMESTAMP NULL,
                reviewed_by VARCHAR(50),
                FOREIGN KEY (from_doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
                FOREIGN KEY (to_doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
                FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE
            )
        ");
        echo "✅ สร้างตาราง shift_change_requests<br>";

        // ตารางผู้ใช้ระบบ
        $pdo_db->exec("
            CREATE TABLE admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) UNIQUE,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                role ENUM('super_admin', 'admin', 'staff', 'viewer') DEFAULT 'staff',
                department_id INT NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
            )
        ");
        echo "✅ สร้างตาราง admin_users<br>";

        // ตารางสิทธิ์การเข้าถึง
        $pdo_db->exec("
            CREATE TABLE permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                module VARCHAR(50) NOT NULL,
                action VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        echo "✅ สร้างตาราง permissions<br>";

        // ตารางสิทธิ์ตามบทบาท
        $pdo_db->exec("
            CREATE TABLE role_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                role ENUM('super_admin', 'admin', 'staff', 'viewer') NOT NULL,
                permission_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
            )
        ");
        echo "✅ สร้างตาราง role_permissions<br>";

        // ตารางบันทึกการใช้งาน
        $pdo_db->exec("
            CREATE TABLE activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                module VARCHAR(50) NOT NULL,
                record_id INT NULL,
                old_data JSON NULL,
                new_data JSON NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
            )
        ");
        echo "✅ สร้างตาราง activity_logs<br>";

        // ตารางการตั้งค่าระบบ
        $pdo_db->exec("
            CREATE TABLE system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                updated_by INT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL
            )
        ");
        echo "✅ สร้างตาราง system_settings<br>";

        // เพิ่มข้อมูลตัวอย่าง
        echo "<h4>เพิ่มข้อมูลตัวอย่าง:</h4>";
        
        // ข้อมูลแผนก
        $departments = [
            ['แผนกอายุรกรรม', 'แผนกรักษาโรคภายใน'],
            ['แผนกศัลยกรรม', 'แผนกผ่าตัดและรักษาโรคภายนอก'],
            ['แผนกกุมารเวชกรรม', 'แผนกรักษาเด็ก'],
            ['แผนกสูติ-นรีเวชกรรม', 'แผนกรักษาสตรีและการคลอด'],
            ['แผนกฉุกเฉิน', 'แผนกรักษาผู้ป่วยฉุกเฉิน'],
            ['แผนกออร์โธปิดิกส์', 'แผนกกระดูกและข้อ'],
            ['แผนกจิตเวชกรรม', 'แผนกรักษาโรคทางจิต'],
            ['แผนกรังสีวิทยา', 'แผนกตรวจเอกซเรย์และภาพถ่าย']
        ];
        
        $stmt = $pdo_db->prepare("INSERT INTO departments (name, description) VALUES (?, ?)");
        foreach ($departments as $dept) {
            $stmt->execute($dept);
        }
        echo "✅ เพิ่มข้อมูลแผนก " . count($departments) . " แผนก<br>";
        
        // ข้อมูลประเภทเวร
        $shift_types = [
            ['เวรเช้า', '08:00:00', '16:00:00', '#28a745', 'เวรประจำเช้า 8:00-16:00'],
            ['เวรบ่าย', '16:00:00', '00:00:00', '#ffc107', 'เวรประจำบ่าย 16:00-24:00'],
            ['เวรดึก', '00:00:00', '08:00:00', '#6f42c1', 'เวรประจำดึก 00:00-08:00'],
            ['เวรฉุกเฉิน', '08:00:00', '08:00:00', '#dc3545', 'เวรฉุกเฉิน 24 ชั่วโมง'],
            ['เวรพิเศษ', '18:00:00', '06:00:00', '#fd7e14', 'เวรพิเศษ 18:00-06:00']
        ];
        
        $stmt = $pdo_db->prepare("INSERT INTO shift_types (name, start_time, end_time, color, description) VALUES (?, ?, ?, ?, ?)");
        foreach ($shift_types as $shift) {
            $stmt->execute($shift);
        }
        echo "✅ เพิ่มข้อมูลประเภทเวร " . count($shift_types) . " ประเภท<br>";
        
        // ข้อมูลแพทย์
        $doctors = [
            ['DOC001', 'สมชาย', 'ใจดี', '<EMAIL>', '081-234-5678', 1, 'แพทย์ประจำ', 'อายุรกรรม', 'MD12345'],
            ['DOC002', 'สมหญิง', 'รักษา', '<EMAIL>', '081-234-5679', 2, 'แพทย์ประจำ', 'ศัลยกรรม', 'MD12346'],
            ['DOC003', 'วิชัย', 'ช่วยคน', '<EMAIL>', '081-234-5680', 3, 'แพทย์ประจำ', 'กุมารเวชกรรม', 'MD12347'],
            ['DOC004', 'สุดา', 'เอาใจใส่', '<EMAIL>', '081-234-5681', 4, 'แพทย์ประจำ', 'สูติ-นรีเวชกรรม', 'MD12348'],
            ['DOC005', 'ประยุทธ', 'ฉุกเฉิน', '<EMAIL>', '081-234-5682', 5, 'แพทย์ประจำ', 'เวชกรรมฉุกเฉิน', 'MD12349'],
            ['DOC006', 'วิภา', 'ดูแล', '<EMAIL>', '081-234-5683', 6, 'แพทย์ประจำ', 'ออร์โธปิดิกส์', 'MD12350'],
            ['DOC007', 'สุรชัย', 'จิตใส', '<EMAIL>', '************', 7, 'แพทย์ประจำ', 'จิตเวชกรรม', 'MD12351'],
            ['DOC008', 'นิภา', 'ส่องแสง', '<EMAIL>', '************', 8, 'แพทย์ประจำ', 'รังสีวิทยา', 'MD12352']
        ];
        
        $stmt = $pdo_db->prepare("INSERT INTO doctors (doctor_code, first_name, last_name, email, phone, department_id, position, specialization, license_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($doctors as $doctor) {
            $stmt->execute($doctor);
        }
        echo "✅ เพิ่มข้อมูลแพทย์ " . count($doctors) . " คน<br>";

        // ข้อมูลผู้ใช้แอดมิน
        $admin_users = [
            ['admin', password_hash('password', PASSWORD_DEFAULT), '<EMAIL>', 'ผู้ดูแล', 'ระบบ', 'super_admin'],
            ['manager', password_hash('password', PASSWORD_DEFAULT), '<EMAIL>', 'ผู้จัดการ', 'ทั่วไป', 'admin'],
            ['staff', password_hash('password', PASSWORD_DEFAULT), '<EMAIL>', 'เจ้าหน้าที่', 'ทั่วไป', 'staff']
        ];

        $stmt = $pdo_db->prepare("INSERT INTO admin_users (username, password, email, first_name, last_name, role) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($admin_users as $user) {
            $stmt->execute($user);
        }
        echo "✅ เพิ่มข้อมูลผู้ใช้แอดมิน " . count($admin_users) . " คน<br>";

        // ข้อมูลสิทธิ์การเข้าถึง
        $permissions = [
            // Doctor Management
            ['view_doctors', 'ดูข้อมูลแพทย์', 'doctors', 'view'],
            ['add_doctors', 'เพิ่มแพทย์ใหม่', 'doctors', 'add'],
            ['edit_doctors', 'แก้ไขข้อมูลแพทย์', 'doctors', 'edit'],
            ['delete_doctors', 'ลบข้อมูลแพทย์', 'doctors', 'delete'],
            // Department Management
            ['view_departments', 'ดูข้อมูลแผนก', 'departments', 'view'],
            ['add_departments', 'เพิ่มแผนกใหม่', 'departments', 'add'],
            ['edit_departments', 'แก้ไขข้อมูลแผนก', 'departments', 'edit'],
            ['delete_departments', 'ลบข้อมูลแผนก', 'departments', 'delete'],
            // Shift Types Management
            ['view_shift_types', 'ดูข้อมูลประเภทเวร', 'shift_types', 'view'],
            ['add_shift_types', 'เพิ่มประเภทเวรใหม่', 'shift_types', 'add'],
            ['edit_shift_types', 'แก้ไขข้อมูลประเภทเวร', 'shift_types', 'edit'],
            ['delete_shift_types', 'ลบข้อมูลประเภทเวร', 'shift_types', 'delete'],
            // Schedule Management
            ['view_schedules', 'ดูตารางเวร', 'schedules', 'view'],
            ['add_schedules', 'เพิ่มตารางเวร', 'schedules', 'add'],
            ['edit_schedules', 'แก้ไขตารางเวร', 'schedules', 'edit'],
            ['delete_schedules', 'ลบตารางเวร', 'schedules', 'delete'],
            // Reports
            ['view_reports', 'ดูรายงาน', 'reports', 'view'],
            ['export_reports', 'ส่งออกรายงาน', 'reports', 'export'],
            // Admin Management
            ['view_users', 'ดูข้อมูลผู้ใช้', 'admin', 'view'],
            ['add_users', 'เพิ่มผู้ใช้ใหม่', 'admin', 'add'],
            ['edit_users', 'แก้ไขข้อมูลผู้ใช้', 'admin', 'edit'],
            ['delete_users', 'ลบข้อมูลผู้ใช้', 'admin', 'delete'],
            // System Settings
            ['view_settings', 'ดูการตั้งค่าระบบ', 'settings', 'view'],
            ['edit_settings', 'แก้ไขการตั้งค่าระบบ', 'settings', 'edit'],
            // Activity Logs
            ['view_logs', 'ดูบันทึกการใช้งาน', 'logs', 'view']
        ];

        $stmt = $pdo_db->prepare("INSERT INTO permissions (name, description, module, action) VALUES (?, ?, ?, ?)");
        foreach ($permissions as $permission) {
            $stmt->execute($permission);
        }
        echo "✅ เพิ่มข้อมูลสิทธิ์การเข้าถึง " . count($permissions) . " รายการ<br>";

        // กำหนดสิทธิ์ตามบทบาท
        // Super Admin: ทุกสิทธิ์
        $pdo_db->exec("INSERT INTO role_permissions (role, permission_id) SELECT 'super_admin', id FROM permissions");

        // Admin: ทุกสิทธิ์ยกเว้นการจัดการผู้ใช้และการตั้งค่าระบบ
        $pdo_db->exec("INSERT INTO role_permissions (role, permission_id) SELECT 'admin', id FROM permissions WHERE module NOT IN ('admin', 'settings', 'logs')");

        // Staff: สิทธิ์พื้นฐานในการดูและจัดการตารางเวร
        $pdo_db->exec("INSERT INTO role_permissions (role, permission_id) SELECT 'staff', id FROM permissions WHERE action IN ('view', 'add', 'edit') AND module IN ('doctors', 'schedules', 'reports')");

        // Viewer: เฉพาะการดูข้อมูล
        $pdo_db->exec("INSERT INTO role_permissions (role, permission_id) SELECT 'viewer', id FROM permissions WHERE action = 'view'");

        echo "✅ กำหนดสิทธิ์ตามบทบาทเรียบร้อยแล้ว<br>";

        // การตั้งค่าระบบเริ่มต้น
        $system_settings = [
            ['hospital_name', 'โรงพยาบาลตัวอย่าง', 'string', 'ชื่อโรงพยาบาล'],
            ['hospital_address', '123 ถนนตัวอย่าง เขตตัวอย่าง กรุงเทพฯ 10100', 'string', 'ที่อยู่โรงพยาบาล'],
            ['hospital_phone', '02-123-4567', 'string', 'เบอร์โทรศัพท์โรงพยาบาล'],
            ['system_timezone', 'Asia/Bangkok', 'string', 'เขตเวลาของระบบ'],
            ['max_schedules_per_day', '3', 'number', 'จำนวนเวรสูงสุดต่อวันต่อแพทย์'],
            ['allow_schedule_overlap', 'false', 'boolean', 'อนุญาตให้มีเวรซ้อนทับกันได้'],
            ['notification_enabled', 'true', 'boolean', 'เปิดใช้งานการแจ้งเตือน'],
            ['backup_enabled', 'true', 'boolean', 'เปิดใช้งานการสำรองข้อมูลอัตโนมัติ']
        ];

        $stmt = $pdo_db->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
        foreach ($system_settings as $setting) {
            $stmt->execute($setting);
        }
        echo "✅ เพิ่มการตั้งค่าระบบ " . count($system_settings) . " รายการ<br>";

        // ข้อมูลตารางเวรตัวอย่าง
        $schedules = [
            // วันนี้
            [1, 1, date('Y-m-d'), 'เวรประจำ', 'admin'],
            [2, 2, date('Y-m-d'), 'เวรประจำ', 'admin'],
            [3, 3, date('Y-m-d'), 'เวรประจำ', 'admin'],
            // พรุ่งนี้
            [4, 1, date('Y-m-d', strtotime('+1 day')), 'เวรประจำ', 'admin'],
            [5, 2, date('Y-m-d', strtotime('+1 day')), 'เวรประจำ', 'admin'],
            [6, 3, date('Y-m-d', strtotime('+1 day')), 'เวรประจำ', 'admin'],
            // วันมะรืนนี้
            [7, 1, date('Y-m-d', strtotime('+2 days')), 'เวรประจำ', 'admin'],
            [8, 2, date('Y-m-d', strtotime('+2 days')), 'เวรประจำ', 'admin'],
            [1, 3, date('Y-m-d', strtotime('+2 days')), 'เวรประจำ', 'admin']
        ];
        
        $stmt = $pdo_db->prepare("INSERT INTO schedules (doctor_id, shift_type_id, schedule_date, notes, created_by) VALUES (?, ?, ?, ?, ?)");
        foreach ($schedules as $schedule) {
            $stmt->execute($schedule);
        }
        echo "✅ เพิ่มข้อมูลตารางเวร " . count($schedules) . " รายการ<br>";
        
        // อัปเดตการตั้งค่าใน config/database.php
        $config_content = file_get_contents('config/database.php');
        $config_content = str_replace("private \$host = 'localhost';", "private \$host = '$host';", $config_content);
        $config_content = str_replace("private \$username = 'root';", "private \$username = '$username';", $config_content);
        $config_content = str_replace("private \$password = '';", "private \$password = '$password';", $config_content);
        file_put_contents('config/database.php', $config_content);
        
        echo "<br><h3>🎉 ตั้งค่าฐานข้อมูลเสร็จสิ้น!</h3>";
        echo "<p>ระบบพร้อมใช้งานแล้ว</p>";
        echo "<a href='index.php' class='btn btn-success btn-lg'>เข้าสู่ระบบตารางเวรแพทย์</a>";
        
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>";
        echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage();
        echo "</div>";
    }
} else {
    // แสดงฟอร์มตั้งค่า
    ?>
    <!DOCTYPE html>
    <html lang="th">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ตั้งค่าฐานข้อมูล</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Sarabun', sans-serif; }
        </style>
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4>ตั้งค่าฐานข้อมูล MySQL</h4>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">Host</label>
                                    <input type="text" class="form-control" name="host" value="localhost" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" name="username" value="root" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" name="password" placeholder="ใส่รหัสผ่าน (หรือเว้นว่างถ้าไม่มี)">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Port</label>
                                    <input type="number" class="form-control" name="port" value="3306" required>
                                </div>
                                <div class="alert alert-warning">
                                    <strong>คำเตือน:</strong> การดำเนินการนี้จะลบฐานข้อมูล 'doctor_schedule' เก่า (ถ้ามี) และสร้างใหม่พร้อมข้อมูลตัวอย่าง
                                </div>
                                <button type="submit" name="setup" class="btn btn-primary w-100">ตั้งค่าฐานข้อมูล</button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="test_connection.php" class="btn btn-info">ทดสอบการเชื่อมต่อ</a>
                        <a href="index.php" class="btn btn-secondary">กลับหน้าหลัก</a>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
