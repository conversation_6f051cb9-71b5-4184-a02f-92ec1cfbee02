<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Migration: เปลี่ยนจาก first_name + last_name เป็น fullname</h2>";
    
    // 1. เพิ่ม column fullname
    echo "<h3>1. เพิ่ม column fullname</h3>";
    try {
        $db->exec("ALTER TABLE doctors ADD COLUMN fullname VARCHAR(100) NOT NULL DEFAULT ''");
        echo "✅ เพิ่ม column fullname สำเร็จ<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "⚠️ Column fullname มีอยู่แล้ว<br>";
        } else {
            echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "<br>";
        }
    }
    
    // 2. อัปเดตข้อมูล fullname จาก first_name + last_name
    echo "<h3>2. อัปเดตข้อมูล fullname</h3>";
    $stmt = $db->prepare("UPDATE doctors SET fullname = CONCAT(first_name, ' ', last_name) WHERE (first_name IS NOT NULL AND first_name != '') AND (last_name IS NOT NULL AND last_name != '')");
    $result = $stmt->execute();
    
    if ($result) {
        $affected = $stmt->rowCount();
        echo "✅ อัปเดตข้อมูล fullname สำเร็จ ($affected แถว)<br>";
    } else {
        echo "❌ เกิดข้อผิดพลาดในการอัปเดตข้อมูล<br>";
    }
    
    // 3. ตรวจสอบผลลัพธ์
    echo "<h3>3. ตรวจสอบผลลัพธ์</h3>";
    $stmt = $db->prepare("SELECT id, doctor_code, fullname, first_name, last_name FROM doctors LIMIT 10");
    $stmt->execute();
    $doctors = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>รหัสแพทย์</th><th>Fullname (ใหม่)</th><th>First Name (เก่า)</th><th>Last Name (เก่า)</th></tr>";
    
    foreach ($doctors as $doctor) {
        echo "<tr>";
        echo "<td>" . $doctor['id'] . "</td>";
        echo "<td>" . $doctor['doctor_code'] . "</td>";
        echo "<td><strong>" . $doctor['fullname'] . "</strong></td>";
        echo "<td>" . $doctor['first_name'] . "</td>";
        echo "<td>" . $doctor['last_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. ตรวจสอบว่ามีข้อมูลที่ fullname ว่าง
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM doctors WHERE fullname = '' OR fullname IS NULL");
    $stmt->execute();
    $empty_count = $stmt->fetch()['count'];
    
    if ($empty_count > 0) {
        echo "<br><div style='color: orange;'>⚠️ พบข้อมูลที่ fullname ว่าง: $empty_count แถว</div>";
        
        $stmt = $db->prepare("SELECT id, doctor_code, first_name, last_name FROM doctors WHERE fullname = '' OR fullname IS NULL");
        $stmt->execute();
        $empty_doctors = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
        echo "<tr><th>ID</th><th>รหัสแพทย์</th><th>First Name</th><th>Last Name</th></tr>";
        foreach ($empty_doctors as $doctor) {
            echo "<tr>";
            echo "<td>" . $doctor['id'] . "</td>";
            echo "<td>" . $doctor['doctor_code'] . "</td>";
            echo "<td>" . $doctor['first_name'] . "</td>";
            echo "<td>" . $doctor['last_name'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<br><div style='color: green;'>✅ ข้อมูล fullname ครบถ้วนทั้งหมด</div>";
    }
    
    echo "<br><h3>4. ขั้นตอนต่อไป</h3>";
    echo "<p>หลังจากตรวจสอบข้อมูลแล้ว สามารถลบ columns เก่าได้โดยรันคำสั่ง:</p>";
    echo "<code>ALTER TABLE doctors DROP COLUMN first_name, DROP COLUMN last_name;</code>";
    
    echo "<br><br><a href='doctors.php'>ไปที่หน้าจัดการแพทย์</a>";
    
} catch (PDOException $e) {
    echo "❌ เกิดข้อผิดพลาดในการเชื่อมต่อฐานข้อมูล: " . $e->getMessage();
}
?>
