<?php
// การเชื่อมต่อฐานข้อมูลโดยตรง
$host = 'localhost';
$dbname = 'doctor_schedule';
$username = 'root';
$password = '123456';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการเชื่อมต่อฐานข้อมูล']);
    exit;
}

// สร้างตาราง leave_requests ถ้ายังไม่มี
try {
    $db->exec("
        CREATE TABLE IF NOT EXISTS leave_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            doctor_id INT NOT NULL,
            leave_type ENUM('single', 'range') NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            leave_category VARCHAR(50) DEFAULT NULL,
            reason TEXT NOT NULL,
            find_replacement BOOLEAN DEFAULT FALSE,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            approved_by INT DEFAULT NULL,
            approved_at DATETIME DEFAULT NULL,
            approval_note TEXT DEFAULT NULL,
            rejected_by INT DEFAULT NULL,
            rejected_at DATETIME DEFAULT NULL,
            rejection_reason TEXT DEFAULT NULL,
            created_by INT NOT NULL DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    $db->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45) DEFAULT NULL,
            user_agent TEXT DEFAULT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (PDOException $e) {
    // ไม่ต้องหยุดการทำงานถ้าตารางมีอยู่แล้ว
}

// จำลองการ login (สำหรับการทดสอบ)
$auth = new stdClass();
$auth->isLoggedIn = function() { return true; };
$auth->getUserId = function() { return 1; };

// ตั้งค่า header สำหรับ JSON response
header('Content-Type: application/json');

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'request':
            handleLeaveRequest();
            break;
        
        case 'list':
            getLeaveList();
            break;
            
        case 'approve':
            approveLeave();
            break;
            
        case 'reject':
            rejectLeave();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

function handleLeaveRequest() {
    global $db, $auth;
    
    // รับข้อมูล JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('ไม่พบข้อมูลคำขอลา');
    }
    
    $doctorId = $input['doctor_id'] ?? null;
    $doctorName = $input['doctor_name'] ?? '';
    $leaveType = $input['leave_type'] ?? '';
    $reason = $input['reason'] ?? '';
    
    if (!$doctorId || !$leaveType || !$reason) {
        throw new Exception('ข้อมูลไม่ครบถ้วน');
    }
    
    // เริ่ม transaction
    $db->beginTransaction();
    
    try {
        if ($leaveType === 'single') {
            // ลาวันเดียว
            $leaveDate = $input['leave_date'] ?? null;
            if (!$leaveDate) {
                throw new Exception('กรุณาระบุวันที่ลา');
            }
            
            // บันทึกคำขอลา
            $stmt = $db->prepare("
                INSERT INTO leave_requests (doctor_id, leave_type, start_date, end_date, reason, status, created_by, created_at)
                VALUES (?, 'single', ?, ?, ?, 'pending', ?, NOW())
            ");
            $stmt->execute([$doctorId, $leaveDate, $leaveDate, $reason, $auth->getUserId()]);
            
            $leaveRequestId = $db->lastInsertId();
            
            // อัพเดทเวรในวันที่ลาให้มีหมายเหตุ
            $stmt = $db->prepare("
                UPDATE schedules 
                SET notes = CONCAT(COALESCE(notes, ''), IF(notes IS NULL OR notes = '', '', ' | '), 'ลา: ', ?)
                WHERE doctor_id = ? AND schedule_date = ?
            ");
            $stmt->execute([$reason, $doctorId, $leaveDate]);
            
        } else if ($leaveType === 'range') {
            // ลาช่วงเวลา
            $startDate = $input['start_date'] ?? null;
            $endDate = $input['end_date'] ?? null;
            $leaveCategory = $input['leave_category'] ?? '';
            $findReplacement = $input['find_replacement'] ?? false;
            
            if (!$startDate || !$endDate) {
                throw new Exception('กรุณาระบุวันที่เริ่มต้นและสิ้นสุด');
            }
            
            // บันทึกคำขอลา
            $stmt = $db->prepare("
                INSERT INTO leave_requests (doctor_id, leave_type, start_date, end_date, leave_category, reason, find_replacement, status, created_by, created_at)
                VALUES (?, 'range', ?, ?, ?, ?, ?, 'pending', ?, NOW())
            ");
            $stmt->execute([$doctorId, $startDate, $endDate, $leaveCategory, $reason, $findReplacement ? 1 : 0, $auth->getUserId()]);
            
            $leaveRequestId = $db->lastInsertId();
            
            // อัพเดทเวรในช่วงวันที่ลาให้มีหมายเหตุ
            $stmt = $db->prepare("
                UPDATE schedules 
                SET notes = CONCAT(COALESCE(notes, ''), IF(notes IS NULL OR notes = '', '', ' | '), 'ลา', ?, ': ', ?)
                WHERE doctor_id = ? AND schedule_date BETWEEN ? AND ?
            ");
            $stmt->execute([$leaveCategory, $reason, $doctorId, $startDate, $endDate]);
        }
        
        // บันทึก log
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action, description, created_at)
            VALUES (?, 'leave_request', ?, NOW())
        ");
        $logDescription = "ส่งคำขอลาสำหรับ {$doctorName} ({$leaveType})";
        $stmt->execute([$auth->getUserId(), $logDescription]);
        
        $db->commit();
        
        echo json_encode([
            'success' => true, 
            'message' => 'ส่งคำขอลาเรียบร้อยแล้ว',
            'leave_request_id' => $leaveRequestId
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

function getLeaveList() {
    global $db;
    
    $departmentId = $_GET['department_id'] ?? null;
    $status = $_GET['status'] ?? 'all';
    $startDate = $_GET['start_date'] ?? null;
    $endDate = $_GET['end_date'] ?? null;
    
    $query = "
        SELECT lr.*, d.fullname as doctor_name, d.doctor_code,
               dept.name as department_name,
               u.username as created_by_name
        FROM leave_requests lr
        JOIN doctors d ON lr.doctor_id = d.id
        LEFT JOIN departments dept ON d.department_id = dept.id
        LEFT JOIN users u ON lr.created_by = u.id
        WHERE 1=1
    ";
    
    $params = [];
    
    if ($departmentId) {
        $query .= " AND d.department_id = ?";
        $params[] = $departmentId;
    }
    
    if ($status !== 'all') {
        $query .= " AND lr.status = ?";
        $params[] = $status;
    }
    
    if ($startDate) {
        $query .= " AND lr.start_date >= ?";
        $params[] = $startDate;
    }
    
    if ($endDate) {
        $query .= " AND lr.end_date <= ?";
        $params[] = $endDate;
    }
    
    $query .= " ORDER BY lr.created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $leaves = $stmt->fetchAll();
    
    echo json_encode(['success' => true, 'data' => $leaves]);
}

function approveLeave() {
    global $db, $auth;
    
    $leaveId = $_POST['leave_id'] ?? null;
    $approvalNote = $_POST['approval_note'] ?? '';
    
    if (!$leaveId) {
        throw new Exception('ไม่พบรหัสคำขอลา');
    }
    
    $db->beginTransaction();
    
    try {
        // อัพเดทสถานะคำขอลา
        $stmt = $db->prepare("
            UPDATE leave_requests 
            SET status = 'approved', 
                approved_by = ?, 
                approved_at = NOW(),
                approval_note = ?
            WHERE id = ?
        ");
        $stmt->execute([$auth->getUserId(), $approvalNote, $leaveId]);
        
        // บันทึก log
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action, description, created_at)
            VALUES (?, 'leave_approve', ?, NOW())
        ");
        $stmt->execute([$auth->getUserId(), "อนุมัติคำขอลา ID: {$leaveId}"]);
        
        $db->commit();
        
        echo json_encode(['success' => true, 'message' => 'อนุมัติคำขอลาเรียบร้อยแล้ว']);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

function rejectLeave() {
    global $db, $auth;
    
    $leaveId = $_POST['leave_id'] ?? null;
    $rejectionReason = $_POST['rejection_reason'] ?? '';
    
    if (!$leaveId || !$rejectionReason) {
        throw new Exception('กรุณาระบุเหตุผลการปฏิเสธ');
    }
    
    $db->beginTransaction();
    
    try {
        // อัพเดทสถานะคำขอลา
        $stmt = $db->prepare("
            UPDATE leave_requests 
            SET status = 'rejected', 
                rejected_by = ?, 
                rejected_at = NOW(),
                rejection_reason = ?
            WHERE id = ?
        ");
        $stmt->execute([$auth->getUserId(), $rejectionReason, $leaveId]);
        
        // ลบหมายเหตุการลาออกจากตารางเวร
        $stmt = $db->prepare("
            SELECT doctor_id, start_date, end_date FROM leave_requests WHERE id = ?
        ");
        $stmt->execute([$leaveId]);
        $leave = $stmt->fetch();
        
        if ($leave) {
            $stmt = $db->prepare("
                UPDATE schedules 
                SET notes = TRIM(BOTH ' | ' FROM REPLACE(REPLACE(notes, CONCAT('ลา: ', ?), ''), 'ลา', ''))
                WHERE doctor_id = ? AND schedule_date BETWEEN ? AND ?
            ");
            $stmt->execute([$rejectionReason, $leave['doctor_id'], $leave['start_date'], $leave['end_date']]);
        }
        
        // บันทึก log
        $stmt = $db->prepare("
            INSERT INTO activity_logs (user_id, action, description, created_at)
            VALUES (?, 'leave_reject', ?, NOW())
        ");
        $stmt->execute([$auth->getUserId(), "ปฏิเสธคำขอลา ID: {$leaveId}"]);
        
        $db->commit();
        
        echo json_encode(['success' => true, 'message' => 'ปฏิเสธคำขอลาเรียบร้อยแล้ว']);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}
?>
