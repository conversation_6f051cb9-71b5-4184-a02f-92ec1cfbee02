# ระบบแอดมินจัดการตารางเวรแพทย์

ระบบแอดมินสำหรับจัดการผู้ใช้ สิทธิ์การเข้าถึง และการตั้งค่าระบบตารางเวรแพทย์

## 🔐 ระบบ Authentication & Authorization

### ฟีเจอร์หลัก
- **ระบบล็อกอิน/ล็อกเอาต์** ที่ปลอดภัย
- **การจัดการสิทธิ์** ตามบทบาท (Role-based Access Control)
- **บันทึกการใช้งาน** (Activity Logs)
- **การเปลี่ยนรหัสผ่าน** พร้อมตรวจสอบความแข็งแกร่ง
- **การจัดการโปรไฟล์** ผู้ใช้

### บทบาทผู้ใช้ (User Roles)

#### 1. **Super Admin** 🔴
- สิทธิ์สูงสุดในระบบ
- จัดการผู้ใช้ทุกคน
- ตั้งค่าระบบ
- ดูบันทึกการใช้งาน
- สำรองข้อมูล

#### 2. **Admin** 🟡
- จัดการข้อมูลแพทย์และแผนก
- จัดตารางเวร
- ดูรายงาน
- ไม่สามารถจัดการผู้ใช้อื่นได้

#### 3. **Staff** 🔵
- ดูและแก้ไขข้อมูลแพทย์
- จัดตารางเวร
- ดูรายงานพื้นฐาน
- ไม่สามารถลบข้อมูลได้

#### 4. **Viewer** 🟢
- ดูข้อมูลเท่านั้น
- ไม่สามารถแก้ไขหรือลบได้

## 📁 ไฟล์ระบบแอดมิน

### ไฟล์หลัก
- **`auth.php`** - คลาส Authentication และ Authorization
- **`login.php`** - หน้าล็อกอิน
- **`logout.php`** - ออกจากระบบ
- **`403.php`** - หน้าแสดงเมื่อไม่มีสิทธิ์

### ไฟล์จัดการผู้ใช้
- **`admin_users.php`** - จัดการผู้ใช้ระบบ
- **`profile.php`** - แก้ไขโปรไฟล์
- **`change_password.php`** - เปลี่ยนรหัสผ่าน

### ไฟล์ส่วนประกอบ
- **`includes/navbar.php`** - เมนูนำทางที่ตรวจสอบสิทธิ์

## 🗄️ โครงสร้างฐานข้อมูล

### ตารางใหม่ที่เพิ่ม

#### `admin_users` - ผู้ใช้ระบบ
```sql
- id (Primary Key)
- username (Unique)
- password (Hashed)
- email
- first_name, last_name
- role (super_admin, admin, staff, viewer)
- department_id (Foreign Key)
- status (active, inactive)
- last_login
- created_at, updated_at
```

#### `permissions` - สิทธิ์การเข้าถึง
```sql
- id (Primary Key)
- name (Permission name)
- description
- module (doctors, schedules, etc.)
- action (view, add, edit, delete)
```

#### `role_permissions` - สิทธิ์ตามบทบาท
```sql
- id (Primary Key)
- role (Enum)
- permission_id (Foreign Key)
```

#### `activity_logs` - บันทึกการใช้งาน
```sql
- id (Primary Key)
- user_id (Foreign Key)
- action, module
- record_id
- old_data, new_data (JSON)
- ip_address, user_agent
- created_at
```

#### `system_settings` - การตั้งค่าระบบ
```sql
- id (Primary Key)
- setting_key (Unique)
- setting_value
- setting_type (string, number, boolean, json)
- description
- updated_by, updated_at
```

## 🚀 การติดตั้งและใช้งาน

### 1. ติดตั้งระบบแอดมิน
```bash
# ตั้งค่าฐานข้อมูลใหม่พร้อมตารางแอดมิน
http://localhost:8000/setup_database.php
```

### 2. บัญชีผู้ใช้เริ่มต้น
| Username | Password | Role | Description |
|----------|----------|------|-------------|
| `admin` | `password` | Super Admin | ผู้ดูแลระบบสูงสุด |
| `manager` | `password` | Admin | ผู้จัดการทั่วไป |
| `staff` | `password` | Staff | เจ้าหน้าที่ |

### 3. เข้าสู่ระบบ
```bash
http://localhost:8000/login.php
```

## 🔧 การใช้งานระบบแอดมิน

### การจัดการผู้ใช้
1. **เข้าสู่ระบบ** ด้วยบัญชี Super Admin หรือ Admin
2. **ไปที่เมนู "ระบบ" > "จัดการผู้ใช้"**
3. **เพิ่มผู้ใช้ใหม่:**
   - กรอกข้อมูลพื้นฐาน
   - เลือกบทบาท
   - กำหนดแผนก (ถ้ามี)
   - ตั้งรหัสผ่านที่แข็งแกร่ง

### การตรวจสอบสิทธิ์ในโค้ด
```php
// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบสิทธิ์เฉพาะ
requirePermission('view_doctors');

// ตรวจสอบสิทธิ์ตามโมดูล
requireModulePermission('doctors', 'add');

// ตรวจสอบในเงื่อนไข
if ($auth->hasPermission('edit_doctors')) {
    // แสดงปุ่มแก้ไข
}
```

### การบันทึก Activity Log
```php
// บันทึกการกระทำ
$auth->logActivity('add_doctor', 'doctors', $doctor_id, null, $new_data);
$auth->logActivity('update_doctor', 'doctors', $doctor_id, $old_data, $new_data);
$auth->logActivity('delete_doctor', 'doctors', $doctor_id, $old_data);
```

## 🛡️ ความปลอดภัย

### การเข้ารหัสรหัสผ่าน
- ใช้ `password_hash()` และ `password_verify()`
- ตรวจสอบความแข็งแกร่งของรหัสผ่าน
- บังคับให้มีตัวพิมพ์ใหญ่ เล็ก ตัวเลข และความยาวอย่างน้อย 8 ตัวอักษร

### การป้องกัน
- **SQL Injection**: ใช้ Prepared Statements
- **XSS**: ใช้ `htmlspecialchars()`
- **CSRF**: ตรวจสอบ Session และ Referer
- **Session Hijacking**: Regenerate Session ID

### การควบคุมการเข้าถึง
- ตรวจสอบสิทธิ์ทุกหน้า
- แยกสิทธิ์ตามบทบาท
- บันทึกการใช้งานทุกการกระทำ

## 📊 การตรวจสอบและบำรุงรักษา

### บันทึกการใช้งาน
- ดูได้ที่ **"ระบบ" > "บันทึกการใช้งาน"**
- แสดงการกระทำทั้งหมดของผู้ใช้
- เก็บข้อมูลเก่าและใหม่ในรูปแบบ JSON

### การตั้งค่าระบบ
- ดูได้ที่ **"ระบบ" > "ตั้งค่าระบบ"**
- กำหนดค่าต่างๆ ของระบบ
- เก็บประวัติการเปลี่ยนแปลง

## 🔄 การอัปเกรดระบบเดิม

หากมีระบบเดิมอยู่แล้ว สามารถอัปเกรดได้โดย:

1. **สำรองข้อมูลเดิม**
2. **รันสคริปต์ `setup_database.php`** (จะเพิ่มตารางใหม่)
3. **อัปเดตไฟล์ PHP** ให้ใช้ระบบ Authentication
4. **เพิ่ม `require_once 'auth.php';` และ `requireLogin();`** ในทุกไฟล์

### ตัวอย่างการอัปเกรด
```php
// เดิม
<?php
require_once 'config/database.php';

// ใหม่
<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบสิทธิ์
requireModulePermission('doctors', 'view');
```

## 🎯 การปรับแต่งเพิ่มเติม

### เพิ่มสิทธิ์ใหม่
1. เพิ่มใน `permissions` table
2. กำหนดให้บทบาทใน `role_permissions`
3. ใช้ `$auth->hasPermission()` ในโค้ด

### เพิ่มบทบาทใหม่
1. แก้ไข ENUM ใน `admin_users.role`
2. แก้ไข ENUM ใน `role_permissions.role`
3. เพิ่มสิทธิ์ในตาราง `role_permissions`

### การแจ้งเตือน
- สามารถเพิ่มระบบแจ้งเตือนผ่าน Email/SMS
- ใช้ข้อมูลจาก `activity_logs` เพื่อส่งการแจ้งเตือน

## 📞 การสนับสนุน

### ปัญหาที่พบบ่อย
1. **ลืมรหัสผ่าน**: ติดต่อ Super Admin เพื่อรีเซ็ต
2. **ไม่มีสิทธิ์**: ตรวจสอบบทบาทและสิทธิ์ที่กำหนด
3. **ไม่สามารถล็อกอินได้**: ตรวจสอบสถานะบัญชี (active/inactive)

### การแก้ไขปัญหาเร่งด่วน
```sql
-- รีเซ็ตรหัสผ่าน admin (password = "password")
UPDATE admin_users 
SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
WHERE username = 'admin';

-- เปิดใช้งานบัญชี
UPDATE admin_users SET status = 'active' WHERE username = 'admin';
```

---

**หมายเหตุ**: ระบบแอดมินนี้ออกแบบมาเพื่อความปลอดภัยและความยืดหยุ่น สามารถปรับแต่งเพิ่มเติมได้ตามความต้องการของแต่ละองค์กร
