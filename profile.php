<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอิน
requireLogin();

$database = new Database();
$db = $database->getConnection();

$message = '';

// ประมวลผลการอัปเดตโปรไฟล์
if ($_POST && isset($_POST['update_profile'])) {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    
    if (empty($first_name) || empty($last_name)) {
        $message = showAlert('กรุณากรอกชื่อและนามสกุล', 'error');
    } else {
        try {
            // ดึงข้อมูลเก่า
            $old_data_query = "SELECT * FROM admin_users WHERE id = ?";
            $old_stmt = $db->prepare($old_data_query);
            $old_stmt->execute([$_SESSION['user_id']]);
            $old_data = $old_stmt->fetch();
            
            // อัปเดตข้อมูล
            $query = "UPDATE admin_users SET first_name = ?, last_name = ?, email = ? WHERE id = ?";
            $stmt = $db->prepare($query);
            
            if ($stmt->execute([$first_name, $last_name, $email, $_SESSION['user_id']])) {
                // อัปเดต session
                $_SESSION['full_name'] = $first_name . ' ' . $last_name;
                
                // บันทึก activity log
                $auth->logActivity('update_profile', 'auth', $_SESSION['user_id'], $old_data, $_POST);
                
                $message = showAlert('อัปเดตโปรไฟล์เรียบร้อยแล้ว', 'success');
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์', 'error');
            }
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                $message = showAlert('อีเมลนี้มีผู้ใช้แล้ว', 'warning');
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์', 'error');
            }
        }
    }
}

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$current_user = $auth->getCurrentUser();

// Role names in Thai
$role_names = [
    'super_admin' => 'ผู้ดูแลระบบสูงสุด',
    'admin' => 'ผู้ดูแลระบบ',
    'staff' => 'เจ้าหน้าที่',
    'viewer' => 'ผู้ดูข้อมูล'
];
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>โปรไฟล์ - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-1">
                    <i class="fas fa-user-edit me-2"></i>
                    โปรไฟล์ของฉัน
                </h1>
                <p class="text-muted">จัดการข้อมูลส่วนตัวและการตั้งค่าบัญชี</p>
            </div>
        </div>

        <div class="row">
            <!-- Profile Info -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div class="doctor-avatar mx-auto" style="width: 100px; height: 100px; font-size: 2.5rem;">
                                <?php echo strtoupper(substr($current_user['first_name'], 0, 1)); ?>
                            </div>
                        </div>
                        
                        <h5 class="card-title">
                            <?php echo htmlspecialchars($current_user['first_name'] . ' ' . $current_user['last_name']); ?>
                        </h5>
                        
                        <p class="text-muted mb-2">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($current_user['username']); ?>
                        </p>
                        
                        <span class="badge bg-<?php echo $current_user['role'] == 'super_admin' ? 'danger' : ($current_user['role'] == 'admin' ? 'warning' : 'primary'); ?> mb-3">
                            <?php echo $role_names[$current_user['role']]; ?>
                        </span>
                        
                        <?php if ($current_user['department_name']): ?>
                            <p class="text-muted mb-2">
                                <i class="fas fa-building me-1"></i>
                                <?php echo htmlspecialchars($current_user['department_name']); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                สมาชิกตั้งแต่ <?php echo formatThaiDate($current_user['created_at']); ?>
                            </small>
                        </div>
                        
                        <?php if ($current_user['last_login']): ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    ล็อกอินล่าสุด <?php echo formatDateTime($current_user['last_login']); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">การดำเนินการด่วน</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="change_password.php" class="btn btn-outline-primary">
                                <i class="fas fa-key me-2"></i>เปลี่ยนรหัสผ่าน
                            </a>
                            
                            <?php if ($auth->hasModulePermission('logs', 'view')): ?>
                                <a href="activity_logs.php?user_id=<?php echo $_SESSION['user_id']; ?>" class="btn btn-outline-info">
                                    <i class="fas fa-history me-2"></i>ประวัติการใช้งาน
                                </a>
                            <?php endif; ?>
                            
                            <a href="logout.php" class="btn btn-outline-danger">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Edit Profile Form -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            แก้ไขข้อมูลโปรไฟล์
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">ชื่อ <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="first_name" 
                                           value="<?php echo htmlspecialchars($current_user['first_name']); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">นามสกุล <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="last_name" 
                                           value="<?php echo htmlspecialchars($current_user['last_name']); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">อีเมล</label>
                                    <input type="email" class="form-control" name="email" 
                                           value="<?php echo htmlspecialchars($current_user['email'] ?? ''); ?>" 
                                           placeholder="<EMAIL>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">ชื่อผู้ใช้</label>
                                    <input type="text" class="form-control" 
                                           value="<?php echo htmlspecialchars($current_user['username']); ?>" 
                                           readonly disabled>
                                    <div class="form-text">ไม่สามารถเปลี่ยนชื่อผู้ใช้ได้</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">บทบาท</label>
                                    <input type="text" class="form-control" 
                                           value="<?php echo $role_names[$current_user['role']]; ?>" 
                                           readonly disabled>
                                    <div class="form-text">ติดต่อผู้ดูแลระบบเพื่อเปลี่ยนบทบาท</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">แผนก</label>
                                    <input type="text" class="form-control" 
                                           value="<?php echo htmlspecialchars($current_user['department_name'] ?? 'ไม่ระบุแผนก'); ?>" 
                                           readonly disabled>
                                    <div class="form-text">ติดต่อผู้ดูแลระบบเพื่อเปลี่ยนแผนก</div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end gap-2">
                                <a href="index.php" class="btn btn-secondary">ยกเลิก</a>
                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Account Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">ข้อมูลบัญชี</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>สถานะบัญชี:</strong>
                                <?php if ($current_user['status'] == 'active'): ?>
                                    <span class="badge bg-success ms-1">ใช้งาน</span>
                                <?php else: ?>
                                    <span class="badge bg-danger ms-1">ไม่ใช้งาน</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-md-6">
                                <strong>สร้างเมื่อ:</strong>
                                <?php echo formatThaiDate($current_user['created_at']); ?>
                            </div>
                            
                            <div class="col-md-6 mt-2">
                                <strong>อัปเดตล่าสุด:</strong>
                                <?php echo formatThaiDate($current_user['updated_at']); ?>
                            </div>
                            
                            <?php if ($current_user['last_login']): ?>
                                <div class="col-md-6 mt-2">
                                    <strong>ล็อกอินล่าสุด:</strong>
                                    <?php echo formatDateTime($current_user['last_login']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/script.js"></script>
</body>
</html>
