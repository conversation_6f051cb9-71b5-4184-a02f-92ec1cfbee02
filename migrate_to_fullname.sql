-- Migration script to change from first_name + last_name to fullname
-- สคริปต์สำหรับเปลี่ยนจาก first_name + last_name เป็น fullname

-- 1. เพิ่ม column fullname
ALTER TABLE doctors ADD COLUMN fullname VARCHAR(100) NOT NULL DEFAULT '';

-- 2. อัปเดตข้อมูล fullname จาก first_name + last_name
UPDATE doctors SET fullname = CONCAT(first_name, ' ', last_name) WHERE first_name IS NOT NULL AND last_name IS NOT NULL;

-- 3. ลบ columns เก่า (ทำหลังจากแน่ใจว่าข้อมูลถูกต้องแล้ว)
-- ALTER TABLE doctors DROP COLUMN first_name;
-- ALTER TABLE doctors DROP COLUMN last_name;

-- ตรวจสอบผลลัพธ์
SELECT id, doctor_code, fullname, first_name, last_name FROM doctors LIMIT 10;
