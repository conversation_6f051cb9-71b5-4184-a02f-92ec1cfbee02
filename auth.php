<?php
// ระบบ Authentication และ Authorization
session_start();

require_once 'config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    // เข้าสู่ระบบ
    public function login($username, $password) {
        try {
            $query = "SELECT u.*, d.name as department_name 
                      FROM admin_users u 
                      LEFT JOIN departments d ON u.department_id = d.id 
                      WHERE u.username = ? AND u.status = 'active'";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // บันทึกข้อมูลใน session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];
                $_SESSION['department_id'] = $user['department_id'];
                $_SESSION['department_name'] = $user['department_name'];
                
                // อัปเดตเวลาล็อกอินล่าสุด
                $update_query = "UPDATE admin_users SET last_login = NOW() WHERE id = ?";
                $update_stmt = $this->db->prepare($update_query);
                $update_stmt->execute([$user['id']]);
                
                // บันทึก activity log
                $this->logActivity('login', 'auth', null, null, ['username' => $username]);
                
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }
    
    // ออกจากระบบ
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity('logout', 'auth');
        }
        
        session_destroy();
        return true;
    }
    
    // ตรวจสอบการล็อกอิน
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    // ตรวจสอบสิทธิ์
    public function hasPermission($permission_name) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Super admin มีสิทธิ์ทุกอย่าง
        if ($_SESSION['role'] === 'super_admin') {
            return true;
        }
        
        try {
            $query = "SELECT COUNT(*) as count 
                      FROM role_permissions rp 
                      JOIN permissions p ON rp.permission_id = p.id 
                      WHERE rp.role = ? AND p.name = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$_SESSION['role'], $permission_name]);
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Permission check error: " . $e->getMessage());
            return false;
        }
    }
    
    // ตรวจสอบสิทธิ์ตามโมดูลและการกระทำ
    public function hasModulePermission($module, $action) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Super admin มีสิทธิ์ทุกอย่าง
        if ($_SESSION['role'] === 'super_admin') {
            return true;
        }
        
        try {
            $query = "SELECT COUNT(*) as count 
                      FROM role_permissions rp 
                      JOIN permissions p ON rp.permission_id = p.id 
                      WHERE rp.role = ? AND p.module = ? AND p.action = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$_SESSION['role'], $module, $action]);
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Module permission check error: " . $e->getMessage());
            return false;
        }
    }
    
    // บันทึก activity log
    public function logActivity($action, $module, $record_id = null, $old_data = null, $new_data = null) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        try {
            $query = "INSERT INTO activity_logs (user_id, action, module, record_id, old_data, new_data, ip_address, user_agent) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($query);
            
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $old_data_json = $old_data ? json_encode($old_data) : null;
            $new_data_json = $new_data ? json_encode($new_data) : null;
            
            return $stmt->execute([
                $_SESSION['user_id'],
                $action,
                $module,
                $record_id,
                $old_data_json,
                $new_data_json,
                $ip_address,
                $user_agent
            ]);
        } catch (Exception $e) {
            error_log("Activity log error: " . $e->getMessage());
            return false;
        }
    }
    
    // ดึงข้อมูลผู้ใช้ปัจจุบัน
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        try {
            $query = "SELECT u.*, d.name as department_name 
                      FROM admin_users u 
                      LEFT JOIN departments d ON u.department_id = d.id 
                      WHERE u.id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$_SESSION['user_id']]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Get current user error: " . $e->getMessage());
            return null;
        }
    }
    
    // ดึงสิทธิ์ทั้งหมดของผู้ใช้ปัจจุบัน
    public function getCurrentUserPermissions() {
        if (!$this->isLoggedIn()) {
            return [];
        }
        
        // Super admin มีสิทธิ์ทุกอย่าง
        if ($_SESSION['role'] === 'super_admin') {
            try {
                $query = "SELECT name FROM permissions";
                $stmt = $this->db->prepare($query);
                $stmt->execute();
                return array_column($stmt->fetchAll(), 'name');
            } catch (Exception $e) {
                return [];
            }
        }
        
        try {
            $query = "SELECT p.name 
                      FROM role_permissions rp 
                      JOIN permissions p ON rp.permission_id = p.id 
                      WHERE rp.role = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$_SESSION['role']]);
            return array_column($stmt->fetchAll(), 'name');
        } catch (Exception $e) {
            error_log("Get user permissions error: " . $e->getMessage());
            return [];
        }
    }
    
    // เปลี่ยนรหัสผ่าน
    public function changePassword($old_password, $new_password) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        try {
            // ตรวจสอบรหัสผ่านเก่า
            $query = "SELECT password FROM admin_users WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($old_password, $user['password'])) {
                return false;
            }
            
            // อัปเดตรหัสผ่านใหม่
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $update_query = "UPDATE admin_users SET password = ? WHERE id = ?";
            $update_stmt = $this->db->prepare($update_query);
            $result = $update_stmt->execute([$hashed_password, $_SESSION['user_id']]);
            
            if ($result) {
                $this->logActivity('change_password', 'auth');
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Change password error: " . $e->getMessage());
            return false;
        }
    }
    
    // ตรวจสอบความแข็งแกร่งของรหัสผ่าน
    public function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'รหัสผ่านต้องมีตัวอักษรพิมพ์ใหญ่อย่างน้อย 1 ตัว';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'รหัสผ่านต้องมีตัวอักษรพิมพ์เล็กอย่างน้อย 1 ตัว';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'รหัสผ่านต้องมีตัวเลขอย่างน้อย 1 ตัว';
        }
        
        return $errors;
    }
}

// ฟังก์ชันช่วยเหลือ
function requireLogin() {
    $auth = new Auth();
    if (!$auth->isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function requirePermission($permission_name) {
    $auth = new Auth();
    if (!$auth->hasPermission($permission_name)) {
        header('HTTP/1.1 403 Forbidden');
        include '403.php';
        exit;
    }
}

function requireModulePermission($module, $action) {
    $auth = new Auth();
    if (!$auth->hasModulePermission($module, $action)) {
        header('HTTP/1.1 403 Forbidden');
        include '403.php';
        exit;
    }
}

// สร้าง instance สำหรับใช้งานทั่วไป
$auth = new Auth();
?>
