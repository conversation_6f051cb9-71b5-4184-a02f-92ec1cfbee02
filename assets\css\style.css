/* ระบบตารางเวรแพทย์ - Custom Styles */

/* Global Styles */
body {
    font-family: '<PERSON>bu<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.1rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.dropdown-menu {
    font-size: 0.875rem;
}

.dropdown-item {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.dropdown-header {
    font-size: 0.875rem;
    font-weight: 600;
}

.badge {
    font-size: 0.7rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success) 0%, #198754 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning) 0%, #fd7e14 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info) 0%, #0dcaf0 100%);
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border: none;
    color: #000;
}

.btn-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #31d2f2 100%);
    border: none;
    color: #000;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
    border: none;
}

/* Forms */
.form-control,
.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Calendar Styles */
.calendar-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 15px 10px;
    min-height: 100px;
    position: relative;
}

.calendar-day.other-month {
    background-color: #f8f9fa;
    color: #6c757d;
}

.calendar-day.today {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 5px;
}

.calendar-event {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.calendar-event:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.calendar-event-actions {
    position: absolute;
    top: 2px;
    right: 2px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.calendar-event:hover .calendar-event-actions {
    opacity: 1;
}

.btn-calendar-action {
    padding: 1px 4px;
    font-size: 0.7rem;
    margin-left: 1px;
    border: none;
}

.btn-calendar-action:hover {
    transform: scale(1.1);
}

/* Schedule Calendar Styles */
.schedule-calendar {
    max-width: 100%;
}

.schedule-day-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.schedule-day-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.schedule-day-card.today-highlight {
    border: 2px solid #007bff;
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
}

.schedule-day-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.schedule-day-content {
    padding: 1.5rem;
}

.schedule-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    transition: all 0.2s ease;
    position: relative;
}

.schedule-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.schedule-card-header {
    margin-bottom: 0.5rem;
}

.schedule-card-footer {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #f1f3f4;
}

/* Shift Group Styles */
.shift-group-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.shift-group-header {
    border-bottom: 1px solid #f1f3f4;
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
}

.doctors-list {
    margin-top: 0.5rem;
}

.doctor-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    transition: all 0.2s ease;
    height: 100%;
}

.doctor-card:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.doctor-avatar-sm {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.doctor-name {
    font-size: 0.9rem;
    color: #007bff;
    font-weight: 600;
}

.doctor-fullname {
    font-size: 0.8rem;
    color: #6c757d;
    line-height: 1.2;
}

/* Doctor Names Display */
.doctors-names-container {
    line-height: 1.6;
}

.doctor-name-tag {
    display: inline-flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 0.25rem 0.5rem;
    margin: 0.1rem;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.doctor-name-tag:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.doctor-avatar-xs {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.7rem;
    flex-shrink: 0;
}

.doctor-display-name {
    font-weight: 500;
    color: #495057;
}

.doctor-separator {
    color: #6c757d;
    margin: 0 0.2rem;
}

.dropdown-menu-sm {
    font-size: 0.8rem;
    min-width: 120px;
}

.btn-xs {
    padding: 0.1rem 0.2rem;
    font-size: 0.7rem;
    line-height: 1;
}

/* Department Selection Cards */
.department-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.department-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #007bff;
}

/* Simple Schedule Calendar */
.schedule-calendar {
    font-size: 0.85rem;
}

.schedule-day-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.schedule-day-card.today-highlight {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.15);
}

.schedule-day-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
}

.schedule-day-header h5 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.schedule-day-header small {
    font-size: 0.75rem;
    color: #6c757d;
}

.schedule-day-content {
    padding: 0.75rem 1rem;
}

.shift-group-card {
    background: #fafbfc;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
}

.shift-group-card:last-child {
    margin-bottom: 0;
}

.shift-group-header h6 {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.shift-group-header .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.shift-group-header small {
    font-size: 0.7rem;
    color: #6c757d;
}

.doctors-names-container {
    margin-top: 0.5rem;
}

.doctor-name-tag {
    display: inline-flex;
    align-items: center;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    font-size: 0.75rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
}

.doctor-name-tag:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
    text-decoration: none;
}

.doctor-avatar-xs {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.6rem;
    font-weight: 600;
    margin-right: 0.25rem;
}

.doctor-display-name {
    font-size: 0.75rem;
    font-weight: 500;
}

.doctor-separator {
    color: #dee2e6;
    margin: 0 0.25rem;
}

/* Compact badges */
.badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Small buttons */
.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
    border-radius: 0.2rem;
}

/* Dropdown menu small */
.dropdown-menu-sm {
    font-size: 0.8rem;
    min-width: 120px;
}

.dropdown-menu-sm .dropdown-item {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

/* Filter Indicator */
.filter-indicator {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #2196f3;
    border-radius: 8px;
    font-size: 0.9rem;
}

.filter-indicator .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}

/* Department Cards Hover Effect */
.department-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.department-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.department-card:hover .card-body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Statistics Cards with Filter State */
.stats-filtered .card {
    position: relative;
    overflow: visible;
}

.stats-filtered .card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #007bff, #28a745, #ffc107, #17a2b8);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.3;
}

/* Day Cards for Schedule Form */
.day-card {
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    cursor: pointer;
}

.day-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.day-card.border-primary {
    border-color: #007bff !important;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

.day-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.day-card.border-primary .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.day-card .form-check-sm .form-check-input {
    margin-top: 0.1rem;
}

.day-card .form-check-sm .form-check-label {
    font-size: 0.8rem;
    line-height: 1.2;
}

/* Schedule Period Cards */
.schedule-period-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.schedule-period-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.schedule-period-card .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: bold;
}

/* Selection Summary */
.selection-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

/* Shift Checkboxes */
.shift-checkbox:disabled + .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
}

.shift-checkbox:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Quick Action Buttons */
.btn-group-sm .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    margin: 0.1rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-group-sm .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Shift Type Buttons */
.shift-type-btn {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
    margin: 0.1rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.shift-type-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.shift-type-btn:hover::before {
    left: 100%;
}

.shift-type-btn:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
}

.shift-type-btn:active {
    transform: translateY(0) scale(0.98);
}

/* Temporary Tooltip */
.temp-tooltip {
    animation: fadeInOut 1.5s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(5px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-5px); }
}

/* Shift Selection Hints */
.shift-selection-hint {
    font-size: 0.65rem;
    color: #6c757d;
    font-style: italic;
    margin-top: 0.25rem;
}

/* Enhanced Day Cards */
.day-card.has-shifts {
    border-color: #28a745 !important;
    background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
}

.day-card.has-shifts .card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

/* Enhanced Shift Type Cards */
.shift-type-card {
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    cursor: pointer;
}

.shift-type-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.shift-type-card.border-primary {
    border-color: #007bff !important;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

.shift-type-card.has-days {
    border-color: #28a745 !important;
    background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
}

.shift-type-card.has-days .card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white !important;
}

.shift-type-card .card-header {
    border-bottom: 1px solid #e9ecef;
}

.shift-type-card.border-primary .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.shift-type-card .form-check-sm .form-check-input {
    margin-top: 0.1rem;
}

.shift-type-card .form-check-sm .form-check-label {
    font-size: 0.8rem;
    line-height: 1.2;
}

/* Counter Badges */
.shift-counter, .day-counter {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    z-index: 10;
}

.day-counter {
    background: #28a745;
}

/* Calendar Navigation */
.calendar-navigation {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.calendar-navigation input[type="date"],
.calendar-navigation input[type="month"] {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.calendar-navigation input[type="date"]:focus,
.calendar-navigation input[type="month"]:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.calendar-navigation .btn-group {
    display: flex;
}

.calendar-navigation .btn-group .btn {
    border-radius: 0;
}

.calendar-navigation .btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.calendar-navigation .btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Calendar Date Display */
.calendar-date-display {
    font-weight: 600;
    color: #495057;
}

.calendar-date-display.today {
    color: #0d6efd;
}

/* Responsive Calendar Navigation */
@media (max-width: 768px) {
    .calendar-navigation {
        flex-direction: column;
        align-items: stretch;
    }

    .calendar-navigation input[type="date"],
    .calendar-navigation input[type="month"] {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .calendar-navigation .btn-group {
        width: 100%;
        justify-content: center;
    }
}

/* Doctor Cards for Index Page */
.doctor-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.doctor-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.doctor-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.doctor-info {
    flex: 1;
}

.doctor-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.doctor-code {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.doctor-department {
    font-size: 0.8rem;
    color: #495057;
    font-style: italic;
}

.shifts-count {
    background: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
}

.shifts-list {
    padding: 1rem;
}

.shift-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.shift-item:last-child {
    margin-bottom: 0;
}

.shift-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.shift-time-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.shift-time {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.shift-name {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    background: rgba(255,255,255,0.8);
    border-radius: 12px;
    border: 1px solid currentColor;
}

.shift-note {
    font-size: 0.8rem;
    color: #6c757d;
    background: white;
    padding: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid #ffc107;
}

/* Responsive Design for Doctor Cards */
@media (max-width: 768px) {
    .doctor-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .shifts-count {
        align-self: flex-end;
    }

    .shift-time-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .shift-name {
        align-self: flex-start;
    }
}

/* Shift Actions Styling */
.shift-actions {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
}

.shift-actions .btn-group {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 6px;
    overflow: hidden;
}

.shift-actions .btn {
    border: none;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.shift-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.shift-actions .btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.shift-actions .btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.shift-actions .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Dropdown Menu Styling */
.shift-actions .dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
    min-width: 200px;
}

.shift-actions .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.shift-actions .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.shift-actions .dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Shift Content Layout */
.shift-content {
    flex: 1;
}

.shift-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Expanded Doctor Card Styling */
.doctor-card.expanded {
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.doctor-card.expanded .doctor-header {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

/* Animation for Shifts List */
.shifts-list {
    transition: all 0.3s ease;
}

.shifts-list .shift-item {
    animation: slideInUp 0.3s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Expand Icon Animation */
.expand-icon {
    transition: transform 0.3s ease;
}

.doctor-card.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .day-card {
        margin-bottom: 1rem;
    }

    .btn-group-sm .btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        margin: 0.1rem;
    }
}

/* Index Page Schedule Cards - Simple & Clean */
.index-schedule-container {
    font-size: 0.85rem;
}

.shift-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.shift-card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    transform: translateY(-1px);
}

.shift-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.shift-time {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    min-width: 100px;
}

.shift-name {
    font-size: 0.8rem;
    color: #6c757d;
    background: #ffffff;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.shift-count {
    font-size: 0.75rem;
    color: #6c757d;
    margin-left: auto;
}

.doctors-list {
    padding: 0;
}

.doctor-item {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.doctor-item:last-child {
    border-bottom: none;
}

.doctor-item:hover {
    background-color: #f8f9fa;
}

.doctor-name {
    font-size: 0.85rem;
    color: #1a365d;
    font-weight: 600;
    margin-bottom: 0.2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 0.1px;
}

.doctor-item:hover .doctor-name {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transform: translateX(1px);
    transition: all 0.3s ease;
}

.doctor-note {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
    padding-left: 0.4rem;
    border-left: 2px solid #e9ecef;
}

/* Compact Doctor List */
.doctors-compact-list {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
}

.doctor-compact-item {
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.doctor-compact-item:hover {
    background: rgba(0, 123, 255, 0.1);
    transform: translateX(2px);
}

.doctor-avatar-sm {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.doctor-compact-info {
    min-width: 0;
}

.doctor-compact-name {
    font-size: 0.9rem;
    line-height: 1.2;
    color: #495057;
}

.doctor-compact-name strong {
    color: #007bff;
    font-weight: 600;
}

.doctor-compact-note {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.2rem;
    line-height: 1.1;
}

.doctor-compact-status {
    flex-shrink: 0;
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: white;
}

.doctor-separator {
    height: 1px;
    background: #e9ecef;
    margin: 0.25rem 0;
}

/* Schedule Timeline */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #0d6efd;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

/* Doctor Card */
.doctor-card {
    border-left: 4px solid #0d6efd;
    transition: all 0.3s ease;
}

.doctor-card:hover {
    border-left-color: #0b5ed7;
    transform: translateX(5px);
}

.doctor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0d6efd, #6610f2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background-color: #28a745;
}

.status-inactive {
    background-color: #dc3545;
}

.status-scheduled {
    background-color: #0d6efd;
}

.status-completed {
    background-color: #28a745;
}

.status-cancelled {
    background-color: #dc3545;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .calendar-day {
        padding: 10px 5px;
        min-height: 80px;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline::before {
        left: 10px;
    }
    
    .timeline-item::before {
        left: -16px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .card-header .btn {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 12px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table thead th {
        background-color: #3d3d3d;
        color: #ffffff;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}
