<?php
// ทดสอบการเชื่อมต่อฐานข้อมูล
header('Content-Type: application/json');

$host = 'localhost';
$dbname = 'doctor_schedule';
$username = 'root';

// ลองหลาย password ที่เป็นไปได้
$passwords = ['', '123456', 'root', 'password', 'Wxmujwsofu@1234'];

$db = null;
$working_password = null;

echo "กำลังทดสอบการเชื่อมต่อฐานข้อมูล...\n";

foreach ($passwords as $password) {
    try {
        echo "ลอง password: '" . ($password ?: '(ว่าง)') . "'...\n";
        $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // ทดสอบการเชื่อมต่อ
        $db->query("SELECT 1");

        echo "✅ เชื่อมต่อฐานข้อมูลสำเร็จด้วย password: '" . ($password ?: '(ว่าง)') . "'\n";
        $working_password = $password;
        break;

    } catch (PDOException $e) {
        echo "❌ ไม่สำเร็จ: " . $e->getMessage() . "\n";
        continue;
    }
}

if ($db === null) {
    echo "❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้ด้วย password ใดๆ\n";
    echo "กรุณาตรวจสอบ:\n";
    echo "1. MySQL Server ทำงานหรือไม่\n";
    echo "2. ฐานข้อมูล 'doctor_schedule' มีอยู่หรือไม่\n";
    echo "3. Username/Password ถูกต้องหรือไม่\n";
    exit;
}

try {
    
    // ตรวจสอบตาราง leave_requests
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'leave_requests'");
        if ($stmt->rowCount() > 0) {
            echo "✅ ตาราง leave_requests มีอยู่แล้ว\n";
        } else {
            echo "❌ ไม่พบตาราง leave_requests\n";
            echo "กำลังสร้างตาราง...\n";
            
            $sql = "
            CREATE TABLE leave_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                doctor_id INT NOT NULL,
                leave_type ENUM('single', 'range') NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                leave_category VARCHAR(50) DEFAULT NULL,
                reason TEXT NOT NULL,
                find_replacement BOOLEAN DEFAULT FALSE,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                approved_by INT DEFAULT NULL,
                approved_at DATETIME DEFAULT NULL,
                approval_note TEXT DEFAULT NULL,
                rejected_by INT DEFAULT NULL,
                rejected_at DATETIME DEFAULT NULL,
                rejection_reason TEXT DEFAULT NULL,
                created_by INT NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->exec($sql);
            echo "✅ สร้างตาราง leave_requests เรียบร้อย\n";
        }
    } catch (Exception $e) {
        echo "❌ ข้อผิดพลาดในการตรวจสอบตาราง: " . $e->getMessage() . "\n";
    }
    
    // ตรวจสอบตาราง activity_logs
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'activity_logs'");
        if ($stmt->rowCount() > 0) {
            echo "✅ ตาราง activity_logs มีอยู่แล้ว\n";
        } else {
            echo "❌ ไม่พบตาราง activity_logs\n";
            echo "กำลังสร้างตาราง...\n";
            
            $sql = "
            CREATE TABLE activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT,
                ip_address VARCHAR(45) DEFAULT NULL,
                user_agent TEXT DEFAULT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->exec($sql);
            echo "✅ สร้างตาราง activity_logs เรียบร้อย\n";
        }
    } catch (Exception $e) {
        echo "❌ ข้อผิดพลาดในการตรวจสอบตาราง activity_logs: " . $e->getMessage() . "\n";
    }
    
    // ทดสอบการเพิ่มข้อมูล
    try {
        $stmt = $db->prepare("
            INSERT INTO leave_requests (doctor_id, leave_type, start_date, end_date, reason, status, created_by)
            VALUES (?, 'single', ?, ?, ?, 'pending', ?)
        ");
        $stmt->execute([1, date('Y-m-d'), date('Y-m-d'), 'ทดสอบระบบ', 1]);
        
        $leaveId = $db->lastInsertId();
        echo "✅ ทดสอบเพิ่มข้อมูลสำเร็จ (ID: $leaveId)\n";
        
        // ลบข้อมูลทดสอบ
        $stmt = $db->prepare("DELETE FROM leave_requests WHERE id = ?");
        $stmt->execute([$leaveId]);
        echo "✅ ลบข้อมูลทดสอบเรียบร้อย\n";
        
    } catch (Exception $e) {
        echo "❌ ข้อผิดพลาดในการทดสอบข้อมูล: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 การทดสอบเสร็จสิ้น - ระบบพร้อมใช้งาน!\n";
    echo "Password ที่ใช้งานได้: '" . ($working_password ?: '(ว่าง)') . "'\n";
    echo "กรุณาอัพเดท leave.php ให้ใช้ password นี้\n";

} catch (Exception $e) {
    echo "❌ ข้อผิดพลาด: " . $e->getMessage() . "\n";
}
?>
