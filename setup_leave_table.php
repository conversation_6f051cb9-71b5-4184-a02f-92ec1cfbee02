<?php
// การเชื่อมต่อฐานข้อมูลโดยตรง
$host = 'localhost';
$dbname = 'doctor_schedule';
$username = 'root';
$password = '123456';

try {
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "เชื่อมต่อฐานข้อมูลสำเร็จ\n";
    echo "กำลังสร้างตาราง leave_requests...\n";
    
    // สร้างตาราง leave_requests
    $sql = "
    CREATE TABLE IF NOT EXISTS leave_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        doctor_id INT NOT NULL,
        leave_type ENUM('single', 'range') NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        leave_category VARCHAR(50) DEFAULT NULL COMMENT 'ประเภทการลา เช่น ลาป่วย, ลากิจ',
        reason TEXT NOT NULL COMMENT 'เหตุผลการลา',
        find_replacement BOOLEAN DEFAULT FALSE COMMENT 'ต้องการหาแพทย์ทดแทนหรือไม่',
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        
        -- ข้อมูลการอนุมัติ
        approved_by INT DEFAULT NULL,
        approved_at DATETIME DEFAULT NULL,
        approval_note TEXT DEFAULT NULL,
        
        -- ข้อมูลการปฏิเสธ
        rejected_by INT DEFAULT NULL,
        rejected_at DATETIME DEFAULT NULL,
        rejection_reason TEXT DEFAULT NULL,
        
        -- ข้อมูลการสร้าง
        created_by INT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Foreign Keys
        FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id),
        FOREIGN KEY (approved_by) REFERENCES users(id),
        FOREIGN KEY (rejected_by) REFERENCES users(id),
        
        -- Indexes
        INDEX idx_doctor_id (doctor_id),
        INDEX idx_status (status),
        INDEX idx_dates (start_date, end_date),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql);
    echo "✅ สร้างตาราง leave_requests เรียบร้อยแล้ว\n";
    
    // สร้างตาราง activity_logs (ถ้ายังไม่มี)
    echo "กำลังสร้างตาราง activity_logs...\n";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        description TEXT,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id),
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql);
    echo "✅ สร้างตาราง activity_logs เรียบร้อยแล้ว\n";
    
    // เพิ่มข้อมูลตัวอย่าง
    echo "กำลังเพิ่มข้อมูลตัวอย่าง...\n";
    
    $sql = "
    INSERT IGNORE INTO leave_requests (doctor_id, leave_type, start_date, end_date, leave_category, reason, status, created_by) VALUES
    (1, 'single', '2024-06-15', '2024-06-15', 'ลาป่วย', 'ไข้หวัดใหญ่', 'approved', 1),
    (2, 'range', '2024-06-20', '2024-06-22', 'ลากิจ', 'ธุระส่วนตัว', 'pending', 1),
    (3, 'single', '2024-06-18', '2024-06-18', 'ลาพักร้อน', 'พักผ่อน', 'rejected', 1)
    ";
    
    $db->exec($sql);
    echo "✅ เพิ่มข้อมูลตัวอย่างเรียบร้อยแล้ว\n";
    
    echo "\n🎉 ติดตั้งระบบการลาเรียบร้อยแล้ว!\n";
    echo "ตอนนี้สามารถใช้งานฟีเจอร์การลาได้แล้ว\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
}
?>
