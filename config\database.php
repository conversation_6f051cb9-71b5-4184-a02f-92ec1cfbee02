<?php
// การเชื่อมต่อฐานข้อมูล
// Database Connection Configuration

class Database {
    private $host = 'localhost';
    private $db_name = 'doctor_schedule';
    private $username = 'root';
    private $password = 'Wxmujwsofu@1234';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            // ลองเชื่อมต่อโดยไม่ระบุฐานข้อมูลก่อน เพื่อสร้างฐานข้อมูลถ้ายังไม่มี
            $dsn_without_db = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $temp_conn = new PDO($dsn_without_db, $this->username, $this->password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);

            // สร้างฐานข้อมูลถ้ายังไม่มี
            $temp_conn->exec("CREATE DATABASE IF NOT EXISTS `{$this->db_name}` CHARACTER SET {$this->charset} COLLATE {$this->charset}_unicode_ci");

            // เชื่อมต่อกับฐานข้อมูลที่สร้างแล้ว
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);

            // ตรวจสอบและสร้างตารางถ้ายังไม่มี
            $this->createTablesIfNotExists();

        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
            echo "<br><br>กรุณาตรวจสอบ:";
            echo "<ul>";
            echo "<li>MySQL Server ทำงานอยู่หรือไม่</li>";
            echo "<li>ชื่อผู้ใช้และรหัสผ่านถูกต้องหรือไม่</li>";
            echo "<li>Host และ Port ถูกต้องหรือไม่</li>";
            echo "</ul>";
        }

        return $this->conn;
    }

    private function createTablesIfNotExists() {
        if (!$this->conn) return;

        try {
            // ตรวจสอบว่ามีตาราง departments หรือไม่
            $stmt = $this->conn->query("SHOW TABLES LIKE 'departments'");
            if ($stmt->rowCount() == 0) {
                // สร้างตารางทั้งหมด
                $this->createAllTables();
                $this->insertSampleData();
            }
        } catch(PDOException $e) {
            echo "Error creating tables: " . $e->getMessage();
        }
    }

    private function createAllTables() {
        $sql = "
        -- ตารางแผนก (Departments)
        CREATE TABLE departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        -- ตารางประเภทเวร (Shift Types)
        CREATE TABLE shift_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            color VARCHAR(7) DEFAULT '#007bff',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        -- ตารางแพทย์ (Doctors)
        CREATE TABLE doctors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            doctor_code VARCHAR(20) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) UNIQUE,
            phone VARCHAR(20),
            department_id INT,
            position VARCHAR(100),
            specialization VARCHAR(100),
            license_number VARCHAR(50),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
        );

        -- ตารางตารางเวร (Schedules)
        CREATE TABLE schedules (
            id INT AUTO_INCREMENT PRIMARY KEY,
            doctor_id INT NOT NULL,
            shift_type_id INT NOT NULL,
            schedule_date DATE NOT NULL,
            notes TEXT,
            status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
            created_by VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
            FOREIGN KEY (shift_type_id) REFERENCES shift_types(id) ON DELETE CASCADE,
            UNIQUE KEY unique_doctor_date_shift (doctor_id, schedule_date, shift_type_id)
        );

        -- ตารางการขอเปลี่ยนเวร (Shift Change Requests)
        CREATE TABLE shift_change_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            from_doctor_id INT NOT NULL,
            to_doctor_id INT NOT NULL,
            schedule_id INT NOT NULL,
            reason TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            reviewed_at TIMESTAMP NULL,
            reviewed_by VARCHAR(50),
            FOREIGN KEY (from_doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
            FOREIGN KEY (to_doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
            FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE
        );
        ";

        // แยกคำสั่ง SQL และรันทีละคำสั่ง
        $statements = explode(';', $sql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $this->conn->exec($statement);
            }
        }
    }

    private function insertSampleData() {
        // ข้อมูลตัวอย่างแผนก
        $departments = [
            ['แผนกอายุรกรรม', 'แผนกรักษาโรคภายใน'],
            ['แผนกศัลยกรรม', 'แผนกผ่าตัดและรักษาโรคภายนอก'],
            ['แผนกกุมารเวชกรรม', 'แผนกรักษาเด็ก'],
            ['แผนกสูติ-นรีเวชกรรม', 'แผนกรักษาสตรีและการคลอด'],
            ['แผนกฉุกเฉิน', 'แผนกรักษาผู้ป่วยฉุกเฉิน'],
            ['แผนกออร์โธปิดิกส์', 'แผนกกระดูกและข้อ'],
            ['แผนกจิตเวชกรรม', 'แผนกรักษาโรคทางจิต'],
            ['แผนกรังสีวิทยา', 'แผนกตรวจเอกซเรย์และภาพถ่าย']
        ];

        $stmt = $this->conn->prepare("INSERT INTO departments (name, description) VALUES (?, ?)");
        foreach ($departments as $dept) {
            $stmt->execute($dept);
        }

        // ข้อมูลตัวอย่างประเภทเวร
        $shift_types = [
            ['เวรเช้า', '08:00:00', '16:00:00', '#28a745', 'เวรประจำเช้า 8:00-16:00'],
            ['เวรบ่าย', '16:00:00', '00:00:00', '#ffc107', 'เวรประจำบ่าย 16:00-24:00'],
            ['เวรดึก', '00:00:00', '08:00:00', '#6f42c1', 'เวรประจำดึก 00:00-08:00'],
            ['เวรฉุกเฉิน', '08:00:00', '08:00:00', '#dc3545', 'เวรฉุกเฉิน 24 ชั่วโมง'],
            ['เวรพิเศษ', '18:00:00', '06:00:00', '#fd7e14', 'เวรพิเศษ 18:00-06:00']
        ];

        $stmt = $this->conn->prepare("INSERT INTO shift_types (name, start_time, end_time, color, description) VALUES (?, ?, ?, ?, ?)");
        foreach ($shift_types as $shift) {
            $stmt->execute($shift);
        }

        // ข้อมูลตัวอย่างแพทย์
        $doctors = [
            ['DOC001', 'สมชาย', 'ใจดี', '<EMAIL>', '081-234-5678', 1, 'แพทย์ประจำ', 'อายุรกรรม', 'MD12345'],
            ['DOC002', 'สมหญิง', 'รักษา', '<EMAIL>', '081-234-5679', 2, 'แพทย์ประจำ', 'ศัลยกรรม', 'MD12346'],
            ['DOC003', 'วิชัย', 'ช่วยคน', '<EMAIL>', '081-234-5680', 3, 'แพทย์ประจำ', 'กุมารเวชกรรม', 'MD12347'],
            ['DOC004', 'สุดา', 'เอาใจใส่', '<EMAIL>', '************', 4, 'แพทย์ประจำ', 'สูติ-นรีเวชกรรม', 'MD12348'],
            ['DOC005', 'ประยุทธ', 'ฉุกเฉิน', '<EMAIL>', '************', 5, 'แพทย์ประจำ', 'เวชกรรมฉุกเฉิน', 'MD12349']
        ];

        $stmt = $this->conn->prepare("INSERT INTO doctors (doctor_code, first_name, last_name, email, phone, department_id, position, specialization, license_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($doctors as $doctor) {
            $stmt->execute($doctor);
        }

        // ข้อมูลตัวอย่างตารางเวร (สัปดาห์นี้)
        $schedules = [
            // วันจันทร์
            [1, 1, date('Y-m-d'), 'เวรประจำ', 'admin'],
            [2, 2, date('Y-m-d'), 'เวรประจำ', 'admin'],
            [3, 3, date('Y-m-d'), 'เวรประจำ', 'admin'],
            // วันอังคาร
            [2, 1, date('Y-m-d', strtotime('+1 day')), 'เวรประจำ', 'admin'],
            [3, 2, date('Y-m-d', strtotime('+1 day')), 'เวรประจำ', 'admin'],
            [4, 3, date('Y-m-d', strtotime('+1 day')), 'เวรประจำ', 'admin'],
            // วันพุธ
            [3, 1, date('Y-m-d', strtotime('+2 days')), 'เวรประจำ', 'admin'],
            [4, 2, date('Y-m-d', strtotime('+2 days')), 'เวรประจำ', 'admin'],
            [5, 3, date('Y-m-d', strtotime('+2 days')), 'เวรประจำ', 'admin']
        ];

        $stmt = $this->conn->prepare("INSERT INTO schedules (doctor_id, shift_type_id, schedule_date, notes, created_by) VALUES (?, ?, ?, ?, ?)");
        foreach ($schedules as $schedule) {
            $stmt->execute($schedule);
        }
    }
}

// ฟังก์ชันช่วยเหลือ
function formatDate($date, $format = 'd/m/Y') {
    return date($format, strtotime($date));
}

function formatTime($time, $format = 'H:i') {
    return date($format, strtotime($time));
}

function formatDateTime($datetime, $format = 'd/m/Y H:i') {
    return date($format, strtotime($datetime));
}

function getThaiDayName($date) {
    $days = [
        'Sunday' => 'อาทิตย์',
        'Monday' => 'จันทร์',
        'Tuesday' => 'อังคาร',
        'Wednesday' => 'พุธ',
        'Thursday' => 'พฤหัสบดี',
        'Friday' => 'ศุกร์',
        'Saturday' => 'เสาร์'
    ];
    
    $dayName = date('l', strtotime($date));
    return $days[$dayName] ?? $dayName;
}

function getThaiMonthName($date) {
    $months = [
        '01' => 'มกราคม', '02' => 'กุมภาพันธ์', '03' => 'มีนาคม',
        '04' => 'เมษายน', '05' => 'พฤษภาคม', '06' => 'มิถุนายน',
        '07' => 'กรกฎาคม', '08' => 'สิงหาคม', '09' => 'กันยายน',
        '10' => 'ตุลาคม', '11' => 'พฤศจิกายน', '12' => 'ธันวาคม'
    ];
    
    $month = date('m', strtotime($date));
    return $months[$month] ?? $month;
}

function formatThaiDate($date) {
    $day = date('j', strtotime($date));
    $month = getThaiMonthName($date);
    $year = date('Y', strtotime($date)) + 543; // แปลงเป็น พ.ศ.
    
    return $day . ' ' . $month . ' ' . $year;
}

// ฟังก์ชันตรวจสอบสิทธิ์ (สำหรับอนาคต)
function checkPermission($permission) {
    // ตรวจสอบสิทธิ์การเข้าถึง
    return true; // ยังไม่ได้ implement
}

// ฟังก์ชันแสดงข้อความแจ้งเตือน
function showAlert($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = $alertClass[$type] ?? 'alert-info';
    
    return '<div class="alert ' . $class . ' alert-dismissible fade show" role="alert">
                ' . htmlspecialchars($message) . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
}

// ฟังก์ชันป้องกัน XSS
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// ฟังก์ชันตรวจสอบว่าเป็นวันหยุดหรือไม่
function isHoliday($date) {
    // ตรวจสอบวันหยุดราชการ (สามารถปรับแต่งได้)
    $holidays = [
        // วันปีใหม่
        date('Y') . '-01-01',
        // วันสงกรานต์
        date('Y') . '-04-13',
        date('Y') . '-04-14',
        date('Y') . '-04-15',
        // วันแรงงาน
        date('Y') . '-05-01',
        // วันเฉลิมพระชนมพรรษา
        date('Y') . '-07-28',
        // วันแม่
        date('Y') . '-08-12',
        // วันปิยมหาราช
        date('Y') . '-10-23',
        // วันพ่อ
        date('Y') . '-12-05',
        // วันรัฐธรรมนูญ
        date('Y') . '-12-10',
        // วันสิ้นปี
        date('Y') . '-12-31'
    ];
    
    return in_array($date, $holidays);
}

// ฟังก์ชันคำนวณจำนวนชั่วโมงการทำงาน
function calculateWorkHours($start_time, $end_time) {
    $start = new DateTime($start_time);
    $end = new DateTime($end_time);

    // ถ้าเวลาสิ้นสุดน้อยกว่าเวลาเริ่มต้น แสดงว่าข้ามวัน
    if ($end < $start) {
        $end->add(new DateInterval('P1D'));
    }

    $interval = $start->diff($end);
    return $interval->h + ($interval->i / 60);
}

// ฟังก์ชันดึงวันหยุดนักขัตฤกษ์ไทย
function getThaiHolidays($year) {
    $holidays = [];

    // วันหยุดที่วันที่แน่นอน
    $fixed_holidays = [
        $year . '-01-01', // วันขึ้นปีใหม่
        $year . '-04-06', // วันจักรี
        $year . '-04-13', // วันสงกรานต์
        $year . '-04-14', // วันสงกรานต์
        $year . '-04-15', // วันสงกรานต์
        $year . '-05-01', // วันแรงงานแห่งชาติ
        $year . '-05-04', // วันฉัตรมงคล
        $year . '-07-28', // วันเฉลิมพระชนมพรรษาพระบาทสมเด็จพระเจ้าอยู่หัว
        $year . '-08-12', // วันแม่แห่งชาติ
        $year . '-10-13', // วันคล้ายวันสวรรคตพระบาทสมเด็จพระบรมชนกาธิเบศร มหาภูมิพลอดุลยเดชมหาราช
        $year . '-10-23', // วันปิยมหาราช
        $year . '-12-05', // วันพ่อแห่งชาติ
        $year . '-12-10', // วันรัฐธรรมนูญ
        $year . '-12-31', // วันสิ้นปี
    ];

    $holidays = array_merge($holidays, $fixed_holidays);

    // วันหยุดที่ขึ้นอยู่กับปฏิทินจันทรคติ (สามารถเพิ่มเติมได้)
    // เช่น วันวิสาขบูชา, วันมาฆบูชา, วันอาสาฬหบูชา, วันเข้าพรรษา

    return $holidays;
}


?>
