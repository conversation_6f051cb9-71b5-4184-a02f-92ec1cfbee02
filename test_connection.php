<?php
// ทดสอบการเชื่อมต่อฐานข้อมูล MySQL

echo "<h2>ทดสอบการเชื่อมต่อฐานข้อมูล MySQL</h2>";

// ตั้งค่าการเชื่อมต่อ
$host = 'localhost';
$username = 'root';
$password = 'Wxmujwsofu@1234';
$port = 3306;

echo "<h3>ข้อมูลการเชื่อมต่อ:</h3>";
echo "Host: $host<br>";
echo "Username: $username<br>";
echo "Password: " . (empty($password) ? '(ไม่มี)' : '(มี)') . "<br>";
echo "Port: $port<br><br>";

// ทดสอบการเชื่อมต่อ MySQL
try {
    echo "<h3>1. ทดสอบการเชื่อมต่อ MySQL Server:</h3>";
    
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ เชื่อมต่อ MySQL Server สำเร็จ<br>";
    
    // แสดงข้อมูล MySQL Server
    $version = $pdo->query("SELECT VERSION() as version")->fetch();
    echo "MySQL Version: " . $version['version'] . "<br><br>";
    
    // ทดสอบสร้างฐานข้อมูล
    echo "<h3>2. ทดสอบสร้างฐานข้อมูล:</h3>";
    
    $pdo->exec("CREATE DATABASE IF NOT EXISTS doctor_schedule CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ สร้างฐานข้อมูล 'doctor_schedule' สำเร็จ<br>";
    
    // เชื่อมต่อกับฐานข้อมูลที่สร้าง
    $dsn_with_db = "mysql:host=$host;port=$port;dbname=doctor_schedule;charset=utf8mb4";
    $pdo_db = new PDO($dsn_with_db, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ เชื่อมต่อฐานข้อมูล 'doctor_schedule' สำเร็จ<br><br>";
    
    // ตรวจสอบตารางที่มีอยู่
    echo "<h3>3. ตรวจสอบตารางในฐานข้อมูล:</h3>";
    
    $tables = $pdo_db->query("SHOW TABLES")->fetchAll();
    if (empty($tables)) {
        echo "ไม่มีตารางในฐานข้อมูล<br>";
        echo "<a href='setup_database.php' class='btn btn-primary'>สร้างตารางและข้อมูลตัวอย่าง</a><br><br>";
    } else {
        echo "ตารางที่มีอยู่:<br>";
        foreach ($tables as $table) {
            $table_name = array_values($table)[0];
            echo "- $table_name<br>";
        }
        echo "<br>";
    }
    
    // ทดสอบการใช้งาน config/database.php
    echo "<h3>4. ทดสอบ config/database.php:</h3>";
    
    require_once 'config/database.php';
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "✅ การเชื่อมต่อผ่าน Database class สำเร็จ<br>";
        
        // ตรวจสอบตารางอีกครั้ง
        $tables_after = $conn->query("SHOW TABLES")->fetchAll();
        echo "จำนวนตารางหลังจากใช้ Database class: " . count($tables_after) . "<br>";
        
        if (count($tables_after) > 0) {
            echo "✅ ระบบพร้อมใช้งาน<br>";
            echo "<a href='index.php' class='btn btn-success'>เข้าสู่ระบบตารางเวรแพทย์</a><br>";
        }
    } else {
        echo "❌ การเชื่อมต่อผ่าน Database class ล้มเหลว<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "<br><br>";
    
    echo "<h3>วิธีแก้ไข:</h3>";
    echo "<ol>";
    echo "<li>ตรวจสอบว่า MySQL Server ทำงานอยู่หรือไม่</li>";
    echo "<li>ตรวจสอบ username และ password</li>";
    echo "<li>ตรวจสอบ port (ปกติคือ 3306)</li>";
    echo "<li>ตรวจสอบ firewall และ security settings</li>";
    echo "</ol>";
    
    echo "<h3>สำหรับ XAMPP:</h3>";
    echo "<ol>";
    echo "<li>เปิด XAMPP Control Panel</li>";
    echo "<li>กด Start ที่ MySQL</li>";
    echo "<li>ตรวจสอบว่า Apache และ MySQL ทำงานอยู่ (สีเขียว)</li>";
    echo "</ol>";
    
    echo "<h3>สำหรับ MySQL Server แยกต่างหาก:</h3>";
    echo "<ol>";
    echo "<li>ตรวจสอบว่า MySQL Service ทำงานอยู่</li>";
    echo "<li>ลองเชื่อมต่อด้วย MySQL Workbench หรือ phpMyAdmin</li>";
    echo "<li>ตรวจสอบ user privileges</li>";
    echo "</ol>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเชื่อมต่อฐานข้อมูล</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Sarabun', sans-serif; margin: 20px; }
        .btn { margin: 10px 5px; }
    </style>
</head>
<body>
    <div class="container">
        <hr>
        <h3>เครื่องมือเพิ่มเติม:</h3>
        <a href="phpinfo.php" class="btn btn-info">ดู PHP Info</a>
        <a href="setup_database.php" class="btn btn-warning">ตั้งค่าฐานข้อมูลใหม่</a>
        <a href="index.php" class="btn btn-primary">กลับหน้าหลัก</a>
    </div>
</body>
</html>
