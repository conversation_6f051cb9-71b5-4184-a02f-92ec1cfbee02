-- ฐานข้อมูลระบบตารางเวรแพทย์
-- Doctor Schedule Management System Database

CREATE DATABASE IF NOT EXISTS doctor_schedule CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE doctor_schedule;

-- ตารางแผนก (Departments)
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ตารางประเภทเวร (Shift Types)
CREATE TABLE shift_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    color VARCHAR(7) DEFAULT '#007bff',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ตารางแพทย์ (Doctors)
CREATE TABLE doctors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_code VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    department_id INT,
    position VARCHAR(100),
    specialization VARCHAR(100),
    license_number VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- ตารางตารางเวร (Schedules)
CREATE TABLE schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_id INT NOT NULL,
    shift_type_id INT NOT NULL,
    schedule_date DATE NOT NULL,
    notes TEXT,
    status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
    FOREIGN KEY (shift_type_id) REFERENCES shift_types(id) ON DELETE CASCADE,
    UNIQUE KEY unique_doctor_date_shift (doctor_id, schedule_date, shift_type_id)
);

-- ตารางการขอเปลี่ยนเวร (Shift Change Requests)
CREATE TABLE shift_change_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    from_doctor_id INT NOT NULL,
    to_doctor_id INT NOT NULL,
    schedule_id INT NOT NULL,
    reason TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewed_by VARCHAR(50),
    FOREIGN KEY (from_doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
    FOREIGN KEY (to_doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
    FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE
);

-- ตารางผู้ใช้ระบบ (Admin Users)
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('super_admin', 'admin', 'staff', 'viewer') DEFAULT 'staff',
    department_id INT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- ตารางสิทธิ์การเข้าถึง (Permissions)
CREATE TABLE permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ตารางสิทธิ์ตามบทบาท (Role Permissions)
CREATE TABLE role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role ENUM('super_admin', 'admin', 'staff', 'viewer') NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- ตารางบันทึกการใช้งาน (Activity Logs)
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    module VARCHAR(50) NOT NULL,
    record_id INT NULL,
    old_data JSON NULL,
    new_data JSON NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- ตารางการตั้งค่าระบบ (System Settings)
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- ข้อมูลตัวอย่างแผนก
INSERT INTO departments (name, description) VALUES
('แผนกอายุรกรรม', 'แผนกรักษาโรคภายใน'),
('แผนกศัลยกรรม', 'แผนกผ่าตัดและรักษาโรคภายนอก'),
('แผนกกุมารเวชกรรม', 'แผนกรักษาเด็ก'),
('แผนกสูติ-นรีเวชกรรม', 'แผนกรักษาสตรีและการคลอด'),
('แผนกฉุกเฉิน', 'แผนกรักษาผู้ป่วยฉุกเฉิน'),
('แผนกออร์โธปิดิกส์', 'แผนกกระดูกและข้อ'),
('แผนกจิตเวชกรรม', 'แผนกรักษาโรคทางจิต'),
('แผนกรังสีวิทยา', 'แผนกตรวจเอกซเรย์และภาพถ่าย');

-- ข้อมูลตัวอย่างประเภทเวร
INSERT INTO shift_types (name, start_time, end_time, color, description) VALUES
('เวรเช้า', '08:00:00', '16:00:00', '#28a745', 'เวรประจำเช้า 8:00-16:00'),
('เวรบ่าย', '16:00:00', '00:00:00', '#ffc107', 'เวรประจำบ่าย 16:00-24:00'),
('เวรดึก', '00:00:00', '08:00:00', '#6f42c1', 'เวรประจำดึก 00:00-08:00'),
('เวรฉุกเฉิน', '08:00:00', '08:00:00', '#dc3545', 'เวรฉุกเฉิน 24 ชั่วโมง'),
('เวรพิเศษ', '18:00:00', '06:00:00', '#fd7e14', 'เวรพิเศษ 18:00-06:00');

-- ข้อมูลตัวอย่างแพทย์
INSERT INTO doctors (doctor_code, first_name, last_name, email, phone, department_id, position, specialization, license_number) VALUES
('DOC001', 'สมชาย', 'ใจดี', '<EMAIL>', '************', 1, 'แพทย์ประจำ', 'อายุรกรรม', 'MD12345'),
('DOC002', 'สมหญิง', 'รักษา', '<EMAIL>', '************', 2, 'แพทย์ประจำ', 'ศัลยกรรม', 'MD12346'),
('DOC003', 'วิชัย', 'ช่วยคน', '<EMAIL>', '************', 3, 'แพทย์ประจำ', 'กุมารเวชกรรม', 'MD12347'),
('DOC004', 'สุดา', 'เอาใจใส่', '<EMAIL>', '************', 4, 'แพทย์ประจำ', 'สูติ-นรีเวชกรรม', 'MD12348'),
('DOC005', 'ประยุทธ', 'ฉุกเฉิน', '<EMAIL>', '************', 5, 'แพทย์ประจำ', 'เวชกรรมฉุกเฉิน', 'MD12349');

-- ข้อมูลผู้ใช้แอดมิน (Admin Users)
INSERT INTO admin_users (username, password, email, first_name, last_name, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'ผู้ดูแล', 'ระบบ', 'super_admin'),
('manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'ผู้จัดการ', 'ทั่วไป', 'admin'),
('staff', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'เจ้าหน้าที่', 'ทั่วไป', 'staff');

-- ข้อมูลสิทธิ์การเข้าถึง (Permissions)
INSERT INTO permissions (name, description, module, action) VALUES
-- Doctor Management
('view_doctors', 'ดูข้อมูลแพทย์', 'doctors', 'view'),
('add_doctors', 'เพิ่มแพทย์ใหม่', 'doctors', 'add'),
('edit_doctors', 'แก้ไขข้อมูลแพทย์', 'doctors', 'edit'),
('delete_doctors', 'ลบข้อมูลแพทย์', 'doctors', 'delete'),

-- Department Management
('view_departments', 'ดูข้อมูลแผนก', 'departments', 'view'),
('add_departments', 'เพิ่มแผนกใหม่', 'departments', 'add'),
('edit_departments', 'แก้ไขข้อมูลแผนก', 'departments', 'edit'),
('delete_departments', 'ลบข้อมูลแผนก', 'departments', 'delete'),

-- Shift Types Management
('view_shift_types', 'ดูข้อมูลประเภทเวร', 'shift_types', 'view'),
('add_shift_types', 'เพิ่มประเภทเวรใหม่', 'shift_types', 'add'),
('edit_shift_types', 'แก้ไขข้อมูลประเภทเวร', 'shift_types', 'edit'),
('delete_shift_types', 'ลบข้อมูลประเภทเวร', 'shift_types', 'delete'),

-- Schedule Management
('view_schedules', 'ดูตารางเวร', 'schedules', 'view'),
('add_schedules', 'เพิ่มตารางเวร', 'schedules', 'add'),
('edit_schedules', 'แก้ไขตารางเวร', 'schedules', 'edit'),
('delete_schedules', 'ลบตารางเวร', 'schedules', 'delete'),

-- Reports
('view_reports', 'ดูรายงาน', 'reports', 'view'),
('export_reports', 'ส่งออกรายงาน', 'reports', 'export'),

-- Admin Management
('view_users', 'ดูข้อมูลผู้ใช้', 'admin', 'view'),
('add_users', 'เพิ่มผู้ใช้ใหม่', 'admin', 'add'),
('edit_users', 'แก้ไขข้อมูลผู้ใช้', 'admin', 'edit'),
('delete_users', 'ลบข้อมูลผู้ใช้', 'admin', 'delete'),

-- System Settings
('view_settings', 'ดูการตั้งค่าระบบ', 'settings', 'view'),
('edit_settings', 'แก้ไขการตั้งค่าระบบ', 'settings', 'edit'),

-- Activity Logs
('view_logs', 'ดูบันทึกการใช้งาน', 'logs', 'view');

-- กำหนดสิทธิ์ตามบทบาท (Role Permissions)
-- Super Admin: ทุกสิทธิ์
INSERT INTO role_permissions (role, permission_id)
SELECT 'super_admin', id FROM permissions;

-- Admin: ทุกสิทธิ์ยกเว้นการจัดการผู้ใช้และการตั้งค่าระบบ
INSERT INTO role_permissions (role, permission_id)
SELECT 'admin', id FROM permissions
WHERE module NOT IN ('admin', 'settings', 'logs');

-- Staff: สิทธิ์พื้นฐานในการดูและจัดการตารางเวร
INSERT INTO role_permissions (role, permission_id)
SELECT 'staff', id FROM permissions
WHERE action IN ('view', 'add', 'edit') AND module IN ('doctors', 'schedules', 'reports');

-- Viewer: เฉพาะการดูข้อมูล
INSERT INTO role_permissions (role, permission_id)
SELECT 'viewer', id FROM permissions
WHERE action = 'view';

-- การตั้งค่าระบบเริ่มต้น (System Settings)
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('hospital_name', 'โรงพยาบาลตัวอย่าง', 'string', 'ชื่อโรงพยาบาล'),
('hospital_address', '123 ถนนตัวอย่าง เขตตัวอย่าง กรุงเทพฯ 10100', 'string', 'ที่อยู่โรงพยาบาล'),
('hospital_phone', '02-123-4567', 'string', 'เบอร์โทรศัพท์โรงพยาบาล'),
('system_timezone', 'Asia/Bangkok', 'string', 'เขตเวลาของระบบ'),
('max_schedules_per_day', '3', 'number', 'จำนวนเวรสูงสุดต่อวันต่อแพทย์'),
('allow_schedule_overlap', 'false', 'boolean', 'อนุญาตให้มีเวรซ้อนทับกันได้'),
('notification_enabled', 'true', 'boolean', 'เปิดใช้งานการแจ้งเตือน'),
('backup_enabled', 'true', 'boolean', 'เปิดใช้งานการสำรองข้อมูลอัตโนมัติ');

-- ข้อมูลตัวอย่างตารางเวร (สัปดาห์นี้)
INSERT INTO schedules (doctor_id, shift_type_id, schedule_date, notes, created_by) VALUES
-- วันจันทร์
(1, 1, CURDATE(), 'เวรประจำ', 'admin'),
(2, 2, CURDATE(), 'เวรประจำ', 'admin'),
(3, 3, CURDATE(), 'เวรประจำ', 'admin'),
-- วันอังคาร
(2, 1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), 'เวรประจำ', 'admin'),
(3, 2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), 'เวรประจำ', 'admin'),
(4, 3, DATE_ADD(CURDATE(), INTERVAL 1 DAY), 'เวรประจำ', 'admin'),
-- วันพุธ
(3, 1, DATE_ADD(CURDATE(), INTERVAL 2 DAY), 'เวรประจำ', 'admin'),
(4, 2, DATE_ADD(CURDATE(), INTERVAL 2 DAY), 'เวรประจำ', 'admin'),
(5, 3, DATE_ADD(CURDATE(), INTERVAL 2 DAY), 'เวรประจำ', 'admin');
