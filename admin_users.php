<?php
require_once 'auth.php';
require_once 'config/database.php';

// ตรวจสอบการล็อกอินและสิทธิ์
requireLogin();
requireModulePermission('admin', 'view');

$database = new Database();
$db = $database->getConnection();

$message = '';
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_user']) && $auth->hasModulePermission('admin', 'add')) {
        // ตรวจสอบความแข็งแกร่งของรหัสผ่าน
        $password_errors = $auth->validatePasswordStrength($_POST['password']);
        if (!empty($password_errors)) {
            $message = showAlert('รหัสผ่านไม่ปลอดภัย: ' . implode(', ', $password_errors), 'error');
        } else {
            $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            
            $query = "INSERT INTO admin_users (username, password, email, first_name, last_name, role, department_id) 
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($query);
            
            try {
                if ($stmt->execute([
                    $_POST['username'],
                    $hashed_password,
                    $_POST['email'],
                    $_POST['first_name'],
                    $_POST['last_name'],
                    $_POST['role'],
                    $_POST['department_id'] ?: null
                ])) {
                    $auth->logActivity('add_user', 'admin', $db->lastInsertId(), null, $_POST);
                    $message = showAlert('เพิ่มผู้ใช้เรียบร้อยแล้ว', 'success');
                    $action = 'list';
                }
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) {
                    $message = showAlert('ชื่อผู้ใช้หรืออีเมลนี้มีอยู่แล้ว', 'warning');
                } else {
                    $message = showAlert('เกิดข้อผิดพลาดในการเพิ่มผู้ใช้', 'error');
                }
            }
        }
    }
    
    if (isset($_POST['update_user']) && $auth->hasModulePermission('admin', 'edit')) {
        $old_data_query = "SELECT * FROM admin_users WHERE id = ?";
        $old_stmt = $db->prepare($old_data_query);
        $old_stmt->execute([$user_id]);
        $old_data = $old_stmt->fetch();
        
        $query = "UPDATE admin_users SET email=?, first_name=?, last_name=?, role=?, department_id=?, status=? WHERE id=?";
        $params = [
            $_POST['email'],
            $_POST['first_name'],
            $_POST['last_name'],
            $_POST['role'],
            $_POST['department_id'] ?: null,
            $_POST['status'],
            $user_id
        ];
        
        // อัปเดตรหัสผ่านถ้ามีการกรอก
        if (!empty($_POST['password'])) {
            $password_errors = $auth->validatePasswordStrength($_POST['password']);
            if (!empty($password_errors)) {
                $message = showAlert('รหัสผ่านไม่ปลอดภัย: ' . implode(', ', $password_errors), 'error');
            } else {
                $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                $query = "UPDATE admin_users SET email=?, first_name=?, last_name=?, role=?, department_id=?, status=?, password=? WHERE id=?";
                $params[] = $hashed_password;
                $params[6] = $params[5]; // swap status and user_id
                $params[5] = $hashed_password;
            }
        }
        
        if (empty($password_errors)) {
            $stmt = $db->prepare($query);
            if ($stmt->execute($params)) {
                $auth->logActivity('update_user', 'admin', $user_id, $old_data, $_POST);
                $message = showAlert('อัปเดตผู้ใช้เรียบร้อยแล้ว', 'success');
                $action = 'list';
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการอัปเดตผู้ใช้', 'error');
            }
        }
    }
    
    if (isset($_POST['delete_user']) && $auth->hasModulePermission('admin', 'delete')) {
        // ป้องกันการลบตัวเอง
        if ($user_id == $_SESSION['user_id']) {
            $message = showAlert('ไม่สามารถลบบัญชีของตัวเองได้', 'warning');
        } else {
            $old_data_query = "SELECT * FROM admin_users WHERE id = ?";
            $old_stmt = $db->prepare($old_data_query);
            $old_stmt->execute([$user_id]);
            $old_data = $old_stmt->fetch();
            
            $query = "DELETE FROM admin_users WHERE id = ?";
            $stmt = $db->prepare($query);
            
            if ($stmt->execute([$user_id])) {
                $auth->logActivity('delete_user', 'admin', $user_id, $old_data);
                $message = showAlert('ลบผู้ใช้เรียบร้อยแล้ว', 'success');
            } else {
                $message = showAlert('เกิดข้อผิดพลาดในการลบผู้ใช้', 'error');
            }
        }
        $action = 'list';
    }
}

// Get departments for dropdown
$dept_query = "SELECT * FROM departments ORDER BY name";
$dept_stmt = $db->prepare($dept_query);
$dept_stmt->execute();
$departments = $dept_stmt->fetchAll();

// Get user data for edit
$user = null;
if ($action == 'edit' && $user_id) {
    $query = "SELECT * FROM admin_users WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
}

// Get all users for list
if ($action == 'list') {
    $search = $_GET['search'] ?? '';
    $role_filter = $_GET['role'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $query = "SELECT u.*, d.name as department_name 
              FROM admin_users u 
              LEFT JOIN departments d ON u.department_id = d.id 
              WHERE 1=1";
    $params = [];
    
    if ($search) {
        $query .= " AND (u.username LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }
    
    if ($role_filter) {
        $query .= " AND u.role = ?";
        $params[] = $role_filter;
    }
    
    if ($status_filter) {
        $query .= " AND u.status = ?";
        $params[] = $status_filter;
    }
    
    $query .= " ORDER BY u.created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
}

// Role names in Thai
$role_names = [
    'super_admin' => 'ผู้ดูแลระบบสูงสุด',
    'admin' => 'ผู้ดูแลระบบ',
    'staff' => 'เจ้าหน้าที่',
    'viewer' => 'ผู้ดูข้อมูล'
];
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการผู้ใช้ระบบ - ระบบตารางเวรแพทย์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-users me-2"></i>
                            จัดการผู้ใช้ระบบ
                        </h1>
                        <p class="text-muted">จัดการบัญชีผู้ใช้และสิทธิ์การเข้าถึง</p>
                    </div>
                    <?php if ($action == 'list' && $auth->hasModulePermission('admin', 'add')): ?>
                        <a href="?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>เพิ่มผู้ใช้ใหม่
                        </a>
                    <?php elseif ($action != 'list'): ?>
                        <a href="admin_users.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>กลับ
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($action == 'list'): ?>
            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">ค้นหา</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" placeholder="ชื่อผู้ใช้, ชื่อ, อีเมล">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">บทบาท</label>
                            <select class="form-select" name="role">
                                <option value="">ทุกบทบาท</option>
                                <?php foreach ($role_names as $role => $name): ?>
                                    <option value="<?php echo $role; ?>" <?php echo ($_GET['role'] ?? '') == $role ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">สถานะ</label>
                            <select class="form-select" name="status">
                                <option value="">ทุกสถานะ</option>
                                <option value="active" <?php echo ($_GET['status'] ?? '') == 'active' ? 'selected' : ''; ?>>ใช้งาน</option>
                                <option value="inactive" <?php echo ($_GET['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>ไม่ใช้งาน</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>ค้นหา
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Users List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        รายชื่อผู้ใช้ระบบ (<?php echo count($users); ?> คน)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่พบข้อมูลผู้ใช้</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ชื่อผู้ใช้</th>
                                        <th>ชื่อ-นามสกุล</th>
                                        <th>อีเมล</th>
                                        <th>บทบาท</th>
                                        <th>แผนก</th>
                                        <th>สถานะ</th>
                                        <th>ล็อกอินล่าสุด</th>
                                        <th>จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $u): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($u['username']); ?></strong>
                                                <?php if ($u['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge bg-info ms-1">คุณ</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($u['first_name'] . ' ' . $u['last_name']); ?></td>
                                            <td><?php echo htmlspecialchars($u['email'] ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $u['role'] == 'super_admin' ? 'danger' : ($u['role'] == 'admin' ? 'warning' : 'primary'); ?>">
                                                    <?php echo $role_names[$u['role']]; ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($u['department_name'] ?? '-'); ?></td>
                                            <td>
                                                <?php if ($u['status'] == 'active'): ?>
                                                    <span class="badge bg-success">ใช้งาน</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">ไม่ใช้งาน</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($u['last_login']): ?>
                                                    <?php echo formatDateTime($u['last_login']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">ยังไม่เคยล็อกอิน</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($auth->hasModulePermission('admin', 'edit')): ?>
                                                        <a href="?action=edit&id=<?php echo $u['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($auth->hasModulePermission('admin', 'delete') && $u['id'] != $_SESSION['user_id']): ?>
                                                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete(<?php echo $u['id']; ?>, '<?php echo htmlspecialchars($u['username']); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action == 'add' || $action == 'edit'): ?>
            <!-- Add/Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-<?php echo $action == 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action == 'add' ? 'เพิ่มผู้ใช้ใหม่' : 'แก้ไขข้อมูลผู้ใช้'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <?php if ($action == 'add'): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">ชื่อผู้ใช้ <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="username" required placeholder="ชื่อผู้ใช้สำหรับล็อกอิน">
                                </div>
                            <?php endif; ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ชื่อ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="first_name" value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">นามสกุล <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="last_name" value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">อีเมล</label>
                                <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">บทบาท <span class="text-danger">*</span></label>
                                <select class="form-select" name="role" required>
                                    <option value="">เลือกบทบาท</option>
                                    <?php foreach ($role_names as $role => $name): ?>
                                        <option value="<?php echo $role; ?>" <?php echo ($user['role'] ?? '') == $role ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">แผนก</label>
                                <select class="form-select" name="department_id">
                                    <option value="">ไม่ระบุแผนก</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" <?php echo ($user['department_id'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($dept['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php if ($action == 'edit'): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">สถานะ</label>
                                    <select class="form-select" name="status">
                                        <option value="active" <?php echo ($user['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>ใช้งาน</option>
                                        <option value="inactive" <?php echo ($user['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>ไม่ใช้งาน</option>
                                    </select>
                                </div>
                            <?php endif; ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    รหัสผ่าน 
                                    <?php if ($action == 'add'): ?>
                                        <span class="text-danger">*</span>
                                    <?php else: ?>
                                        <small class="text-muted">(เว้นว่างหากไม่ต้องการเปลี่ยน)</small>
                                    <?php endif; ?>
                                </label>
                                <input type="password" class="form-control" name="password" <?php echo $action == 'add' ? 'required' : ''; ?> placeholder="รหัสผ่านอย่างน้อย 8 ตัวอักษร">
                                <div class="form-text">
                                    รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร ประกอบด้วยตัวพิมพ์ใหญ่ พิมพ์เล็ก และตัวเลข
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="admin_users.php" class="btn btn-secondary">ยกเลิก</a>
                            <button type="submit" name="<?php echo $action == 'add' ? 'add_user' : 'update_user'; ?>" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?php echo $action == 'add' ? 'เพิ่มผู้ใช้' : 'บันทึกการแก้ไข'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณต้องการลบผู้ใช้ <strong id="userName"></strong> หรือไม่?</p>
                    <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถยกเลิกได้</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" id="deleteId" name="user_id">
                        <button type="submit" name="delete_user" class="btn btn-danger">ลบผู้ใช้</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(id, username) {
            document.getElementById('deleteId').value = id;
            document.getElementById('userName').textContent = username;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
