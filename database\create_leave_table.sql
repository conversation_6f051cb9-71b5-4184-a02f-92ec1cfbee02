-- สร้างตาราง leave_requests สำหรับจัดการคำขอลา
CREATE TABLE IF NOT EXISTS leave_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_id INT NOT NULL,
    leave_type ENUM('single', 'range') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    leave_category VARCHAR(50) DEFAULT NULL COMMENT 'ประเภทการลา เช่น ลาป่วย, ลากิจ',
    reason TEXT NOT NULL COMMENT 'เหตุผลการลา',
    find_replacement BOOLEAN DEFAULT FALSE COMMENT 'ต้องการหาแพทย์ทดแทนหรือไม่',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    
    -- ข้อมูลการอนุมัติ
    approved_by INT DEFAULT NULL,
    approved_at DATETIME DEFAULT NULL,
    approval_note TEXT DEFAULT NULL,
    
    -- ข้อมูลการปฏิเสธ
    rejected_by INT DEFAULT NULL,
    rejected_at DATETIME DEFAULT NULL,
    rejection_reason TEXT DEFAULT NULL,
    
    -- ข้อมูลการสร้าง
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    FOREIGN KEY (rejected_by) REFERENCES users(id),
    
    -- Indexes
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- สร้างตาราง activity_logs สำหรับบันทึกกิจกรรม (ถ้ายังไม่มี)
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- เพิ่มข้อมูลตัวอย่าง leave_requests
INSERT INTO leave_requests (doctor_id, leave_type, start_date, end_date, leave_category, reason, status, created_by) VALUES
(1, 'single', '2024-06-15', '2024-06-15', 'ลาป่วย', 'ไข้หวัดใหญ่', 'approved', 1),
(2, 'range', '2024-06-20', '2024-06-22', 'ลากิจ', 'ธุระส่วนตัว', 'pending', 1),
(3, 'single', '2024-06-18', '2024-06-18', 'ลาพักร้อน', 'พักผ่อน', 'rejected', 1);
